#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام المحاسبة المتكامل
Integrated Accounting System

المطور: مساعد الذكي
تاريخ الإنشاء: 2025-09-05
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sqlite3
import os
from datetime import datetime
import sys

# إضافة مسار المجلدات للاستيراد
sys.path.append(os.path.join(os.path.dirname(__file__), 'modules'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'database'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'ui'))

from database.db_manager import DatabaseManager
from ui.main_window import MainWindow

class AccountingSystem:
    """الفئة الرئيسية لنظام المحاسبة"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.setup_main_window()
        self.db_manager = DatabaseManager()
        self.main_window = None
        
    def setup_main_window(self):
        """إعداد النافذة الرئيسية"""
        self.root.title("نظام المحاسبة المتكامل - Integrated Accounting System")
        self.root.geometry("1200x800")
        self.root.state('zoomed')  # فتح النافذة بحجم كامل
        
        # دعم اللغة العربية
        self.root.option_add('*Font', 'Arial 12')
        
        # إعداد الأيقونة (إذا كانت متوفرة)
        try:
            self.root.iconbitmap('assets/icon.ico')
        except:
            pass
            
    def initialize_database(self):
        """تهيئة قاعدة البيانات"""
        try:
            self.db_manager.create_tables()
            print("تم إنشاء قاعدة البيانات بنجاح")
        except Exception as e:
            messagebox.showerror("خطأ في قاعدة البيانات", f"حدث خطأ في إنشاء قاعدة البيانات:\n{str(e)}")
            return False
        return True
        
    def run(self):
        """تشغيل التطبيق"""
        if self.initialize_database():
            self.main_window = MainWindow(self.root, self.db_manager)
            self.root.mainloop()
        else:
            print("فشل في تهيئة قاعدة البيانات")

def main():
    """الدالة الرئيسية"""
    try:
        app = AccountingSystem()
        app.run()
    except Exception as e:
        print(f"خطأ في تشغيل التطبيق: {str(e)}")
        messagebox.showerror("خطأ", f"حدث خطأ في تشغيل التطبيق:\n{str(e)}")

if __name__ == "__main__":
    main()
