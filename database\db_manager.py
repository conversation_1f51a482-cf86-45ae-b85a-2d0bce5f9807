#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير قاعدة البيانات
Database Manager

يحتوي على جميع العمليات المتعلقة بقاعدة البيانات
"""

import sqlite3
import os
from datetime import datetime
import threading

class DatabaseManager:
    """فئة إدارة قاعدة البيانات"""
    
    def __init__(self, db_path="accounting_system.db"):
        self.db_path = db_path
        self.lock = threading.Lock()
        self.create_database()
        
    def create_database(self):
        """إنشاء قاعدة البيانات إذا لم تكن موجودة"""
        if not os.path.exists(self.db_path):
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("PRAGMA foreign_keys = ON")
                
    def get_connection(self):
        """الحصول على اتصال بقاعدة البيانات"""
        conn = sqlite3.connect(self.db_path)
        conn.execute("PRAGMA foreign_keys = ON")
        conn.row_factory = sqlite3.Row
        return conn
        
    def create_tables(self):
        """إنشاء جميع الجداول"""
        with self.lock:
            conn = self.get_connection()
            try:
                # جدول العملاء
                conn.execute('''
                    CREATE TABLE IF NOT EXISTS customers (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        name TEXT NOT NULL,
                        phone TEXT,
                        address TEXT,
                        email TEXT,
                        balance REAL DEFAULT 0,
                        created_date TEXT DEFAULT CURRENT_TIMESTAMP,
                        notes TEXT
                    )
                ''')
                
                # جدول الموردين
                conn.execute('''
                    CREATE TABLE IF NOT EXISTS suppliers (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        name TEXT NOT NULL,
                        phone TEXT,
                        address TEXT,
                        email TEXT,
                        balance REAL DEFAULT 0,
                        created_date TEXT DEFAULT CURRENT_TIMESTAMP,
                        notes TEXT
                    )
                ''')
                
                # جدول المنتجات
                conn.execute('''
                    CREATE TABLE IF NOT EXISTS products (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        name TEXT NOT NULL,
                        barcode TEXT UNIQUE,
                        unit TEXT DEFAULT 'قطعة',
                        purchase_price REAL DEFAULT 0,
                        sale_price REAL DEFAULT 0,
                        quantity REAL DEFAULT 0,
                        min_quantity REAL DEFAULT 0,
                        category TEXT,
                        created_date TEXT DEFAULT CURRENT_TIMESTAMP,
                        notes TEXT
                    )
                ''')
                
                # جدول فواتير المبيعات
                conn.execute('''
                    CREATE TABLE IF NOT EXISTS sales_invoices (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        invoice_number TEXT UNIQUE NOT NULL,
                        customer_id INTEGER,
                        invoice_date TEXT DEFAULT CURRENT_TIMESTAMP,
                        total_amount REAL DEFAULT 0,
                        discount REAL DEFAULT 0,
                        tax REAL DEFAULT 0,
                        final_amount REAL DEFAULT 0,
                        paid_amount REAL DEFAULT 0,
                        remaining_amount REAL DEFAULT 0,
                        payment_method TEXT DEFAULT 'نقدي',
                        notes TEXT,
                        status TEXT DEFAULT 'مفتوحة',
                        FOREIGN KEY (customer_id) REFERENCES customers (id)
                    )
                ''')
                
                # جدول تفاصيل فواتير المبيعات
                conn.execute('''
                    CREATE TABLE IF NOT EXISTS sales_invoice_items (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        invoice_id INTEGER NOT NULL,
                        product_id INTEGER NOT NULL,
                        quantity REAL NOT NULL,
                        unit_price REAL NOT NULL,
                        total_price REAL NOT NULL,
                        FOREIGN KEY (invoice_id) REFERENCES sales_invoices (id) ON DELETE CASCADE,
                        FOREIGN KEY (product_id) REFERENCES products (id)
                    )
                ''')
                
                # جدول فواتير المشتريات
                conn.execute('''
                    CREATE TABLE IF NOT EXISTS purchase_invoices (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        invoice_number TEXT UNIQUE NOT NULL,
                        supplier_id INTEGER,
                        invoice_date TEXT DEFAULT CURRENT_TIMESTAMP,
                        total_amount REAL DEFAULT 0,
                        discount REAL DEFAULT 0,
                        tax REAL DEFAULT 0,
                        final_amount REAL DEFAULT 0,
                        paid_amount REAL DEFAULT 0,
                        remaining_amount REAL DEFAULT 0,
                        payment_method TEXT DEFAULT 'نقدي',
                        notes TEXT,
                        status TEXT DEFAULT 'مفتوحة',
                        FOREIGN KEY (supplier_id) REFERENCES suppliers (id)
                    )
                ''')
                
                # جدول تفاصيل فواتير المشتريات
                conn.execute('''
                    CREATE TABLE IF NOT EXISTS purchase_invoice_items (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        invoice_id INTEGER NOT NULL,
                        product_id INTEGER NOT NULL,
                        quantity REAL NOT NULL,
                        unit_price REAL NOT NULL,
                        total_price REAL NOT NULL,
                        FOREIGN KEY (invoice_id) REFERENCES purchase_invoices (id) ON DELETE CASCADE,
                        FOREIGN KEY (product_id) REFERENCES products (id)
                    )
                ''')
                
                # جدول الحسابات المصرفية
                conn.execute('''
                    CREATE TABLE IF NOT EXISTS bank_accounts (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        account_name TEXT NOT NULL,
                        bank_name TEXT NOT NULL,
                        account_number TEXT UNIQUE,
                        balance REAL DEFAULT 0,
                        created_date TEXT DEFAULT CURRENT_TIMESTAMP,
                        notes TEXT
                    )
                ''')
                
                # جدول حركات الخزنة والبنك
                conn.execute('''
                    CREATE TABLE IF NOT EXISTS cash_bank_transactions (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        transaction_type TEXT NOT NULL, -- 'cash_in', 'cash_out', 'bank_in', 'bank_out', 'transfer'
                        account_id INTEGER, -- للحسابات المصرفية
                        amount REAL NOT NULL,
                        description TEXT,
                        reference_type TEXT, -- 'sales_invoice', 'purchase_invoice', 'manual'
                        reference_id INTEGER,
                        transaction_date TEXT DEFAULT CURRENT_TIMESTAMP,
                        notes TEXT,
                        FOREIGN KEY (account_id) REFERENCES bank_accounts (id)
                    )
                ''')
                
                # جدول المدفوعات
                conn.execute('''
                    CREATE TABLE IF NOT EXISTS payments (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        payment_type TEXT NOT NULL, -- 'customer_payment', 'supplier_payment'
                        customer_id INTEGER,
                        supplier_id INTEGER,
                        amount REAL NOT NULL,
                        payment_method TEXT DEFAULT 'نقدي',
                        account_id INTEGER, -- للدفع البنكي
                        payment_date TEXT DEFAULT CURRENT_TIMESTAMP,
                        notes TEXT,
                        FOREIGN KEY (customer_id) REFERENCES customers (id),
                        FOREIGN KEY (supplier_id) REFERENCES suppliers (id),
                        FOREIGN KEY (account_id) REFERENCES bank_accounts (id)
                    )
                ''')
                
                conn.commit()
                print("تم إنشاء جميع الجداول بنجاح")
                
            except Exception as e:
                conn.rollback()
                print(f"خطأ في إنشاء الجداول: {str(e)}")
                raise
            finally:
                conn.close()
                
    def execute_query(self, query, params=None):
        """تنفيذ استعلام"""
        with self.lock:
            conn = self.get_connection()
            try:
                cursor = conn.cursor()
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)
                conn.commit()
                return cursor.fetchall()
            except Exception as e:
                conn.rollback()
                raise e
            finally:
                conn.close()
                
    def execute_insert(self, query, params=None):
        """تنفيذ استعلام إدراج وإرجاع ID الصف الجديد"""
        with self.lock:
            conn = self.get_connection()
            try:
                cursor = conn.cursor()
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)
                conn.commit()
                return cursor.lastrowid
            except Exception as e:
                conn.rollback()
                raise e
            finally:
                conn.close()
                
    def get_next_invoice_number(self, invoice_type='sales'):
        """الحصول على رقم الفاتورة التالي"""
        table_name = 'sales_invoices' if invoice_type == 'sales' else 'purchase_invoices'
        query = f"SELECT MAX(CAST(SUBSTR(invoice_number, 2) AS INTEGER)) FROM {table_name} WHERE invoice_number LIKE ?"
        prefix = 'S' if invoice_type == 'sales' else 'P'
        
        result = self.execute_query(query, (f"{prefix}%",))
        max_num = result[0][0] if result and result[0][0] else 0
        return f"{prefix}{max_num + 1:06d}"
