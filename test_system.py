#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار النظام
System Testing
"""

import sys
import os
import sqlite3
from datetime import datetime

# إضافة مسار المجلدات للاستيراد
sys.path.append(os.path.join(os.path.dirname(__file__), 'database'))

def test_database_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    try:
        from database.db_manager import DatabaseManager
        db = DatabaseManager()
        print("✓ تم الاتصال بقاعدة البيانات بنجاح")
        return True
    except Exception as e:
        print(f"✗ خطأ في الاتصال بقاعدة البيانات: {str(e)}")
        return False

def test_database_tables():
    """اختبار إنشاء الجداول"""
    try:
        from database.db_manager import DatabaseManager
        db = DatabaseManager()
        db.create_tables()
        print("✓ تم إنشاء جميع الجداول بنجاح")
        return True
    except Exception as e:
        print(f"✗ خطأ في إنشاء الجداول: {str(e)}")
        return False

def test_models():
    """اختبار نماذج البيانات"""
    try:
        from database.db_manager import DatabaseManager
        from database.models import Customer, Supplier, Product
        
        db = DatabaseManager()
        
        # اختبار نموذج العميل
        customer = Customer(db, name="عميل تجريبي", phone="01234567890", 
                          address="عنوان تجريبي", email="<EMAIL>")
        customer_id = customer.save()
        print(f"✓ تم إنشاء عميل تجريبي بالمعرف: {customer_id}")
        
        # اختبار نموذج المورد
        supplier = Supplier(db, name="مورد تجريبي", phone="01234567890", 
                          address="عنوان تجريبي", email="<EMAIL>")
        supplier_id = supplier.save()
        print(f"✓ تم إنشاء مورد تجريبي بالمعرف: {supplier_id}")
        
        # اختبار نموذج المنتج
        product = Product(db, name="منتج تجريبي", barcode="123456789", 
                         unit="قطعة", purchase_price=10.0, sale_price=15.0, 
                         quantity=100, min_quantity=10)
        product_id = product.save()
        print(f"✓ تم إنشاء منتج تجريبي بالمعرف: {product_id}")
        
        return True
    except Exception as e:
        print(f"✗ خطأ في اختبار النماذج: {str(e)}")
        return False

def test_ui_imports():
    """اختبار استيراد واجهات المستخدم"""
    try:
        from ui.main_window import MainWindow
        print("✓ تم استيراد النافذة الرئيسية بنجاح")
        
        from ui.sales_invoice_window import SalesInvoiceWindow
        print("✓ تم استيراد نافذة فواتير المبيعات بنجاح")
        
        from ui.customers_window import CustomersWindow
        print("✓ تم استيراد نافذة إدارة العملاء بنجاح")
        
        from ui.suppliers_window import SuppliersWindow
        print("✓ تم استيراد نافذة إدارة الموردين بنجاح")
        
        from ui.products_window import ProductsWindow
        print("✓ تم استيراد نافذة إدارة المنتجات بنجاح")
        
        from ui.cash_management_window import CashManagementWindow
        print("✓ تم استيراد نافذة إدارة الخزنة بنجاح")
        
        from ui.analytics_window import AnalyticsWindow
        print("✓ تم استيراد نافذة التحليلات بنجاح")
        
        return True
    except Exception as e:
        print(f"✗ خطأ في استيراد الواجهات: {str(e)}")
        return False

def test_config():
    """اختبار ملف التكوين"""
    try:
        import config
        info = config.get_system_info()
        print(f"✓ تم تحميل التكوين بنجاح - الإصدار: {info['version']}")
        return True
    except Exception as e:
        print(f"✗ خطأ في تحميل التكوين: {str(e)}")
        return False

def test_folders():
    """اختبار إنشاء المجلدات"""
    try:
        required_folders = ['database', 'ui', 'modules', 'assets', 'reports']
        missing_folders = []
        
        for folder in required_folders:
            if not os.path.exists(folder):
                missing_folders.append(folder)
        
        if missing_folders:
            print(f"✗ المجلدات المفقودة: {', '.join(missing_folders)}")
            return False
        else:
            print("✓ جميع المجلدات المطلوبة موجودة")
            return True
    except Exception as e:
        print(f"✗ خطأ في فحص المجلدات: {str(e)}")
        return False

def test_requirements():
    """اختبار المتطلبات"""
    try:
        import tkinter
        print("✓ tkinter متوفر")
        
        import sqlite3
        print("✓ sqlite3 متوفر")
        
        import datetime
        print("✓ datetime متوفر")
        
        # اختبار المكتبات الاختيارية
        optional_libs = [
            'arabic_reshaper',
            'bidi',
            'reportlab',
            'matplotlib',
            'pandas',
            'openpyxl',
            'PIL',
            'pyzbar',
            'cv2'
        ]
        
        available_libs = []
        missing_libs = []
        
        for lib in optional_libs:
            try:
                __import__(lib)
                available_libs.append(lib)
            except ImportError:
                missing_libs.append(lib)
        
        if available_libs:
            print(f"✓ المكتبات المتوفرة: {', '.join(available_libs)}")
        
        if missing_libs:
            print(f"⚠ المكتبات المفقودة (اختيارية): {', '.join(missing_libs)}")
        
        return True
    except Exception as e:
        print(f"✗ خطأ في فحص المتطلبات: {str(e)}")
        return False

def run_all_tests():
    """تشغيل جميع الاختبارات"""
    print("=" * 60)
    print("اختبار نظام المحاسبة المتكامل")
    print("Integrated Accounting System Testing")
    print("=" * 60)
    print()
    
    tests = [
        ("فحص المجلدات", test_folders),
        ("فحص المتطلبات", test_requirements),
        ("فحص التكوين", test_config),
        ("اختبار قاعدة البيانات", test_database_connection),
        ("اختبار الجداول", test_database_tables),
        ("اختبار النماذج", test_models),
        ("اختبار الواجهات", test_ui_imports)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * 30)
        try:
            if test_func():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"✗ خطأ غير متوقع: {str(e)}")
            failed += 1
    
    print("\n" + "=" * 60)
    print("نتائج الاختبار:")
    print(f"✓ نجح: {passed}")
    print(f"✗ فشل: {failed}")
    print(f"المجموع: {passed + failed}")
    
    if failed == 0:
        print("\n🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام")
        return True
    else:
        print(f"\n⚠ فشل {failed} اختبار. يرجى مراجعة الأخطاء أعلاه")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    
    print("\n" + "=" * 60)
    if success:
        print("يمكنك الآن تشغيل النظام باستخدام: python main.py")
        print("أو استخدام: run.bat")
    else:
        print("يرجى إصلاح الأخطاء قبل تشغيل النظام")
    
    input("\nاضغط Enter للخروج...")
