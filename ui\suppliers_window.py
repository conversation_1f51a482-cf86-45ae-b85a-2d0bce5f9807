#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة إدارة الموردين
Suppliers Management Window
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# إضافة مسار المجلدات للاستيراد
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'database'))

from database.models import Supplier

class SuppliersWindow:
    """فئة نافذة إدارة الموردين"""
    
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.window = tk.Toplevel(parent)
        self.suppliers = []
        self.selected_supplier = None
        
        self.setup_window()
        self.setup_ui()
        self.load_suppliers()
        self.setup_keyboard_shortcuts()
        
    def setup_window(self):
        """إعداد النافذة"""
        self.window.title("إدارة الموردين")
        self.window.geometry("900x600")
        self.window.resizable(True, True)
        
        # جعل النافذة في المقدمة
        self.window.transient(self.parent)
        self.window.grab_set()
        
        # توسيط النافذة
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (900 // 2)
        y = (self.window.winfo_screenheight() // 2) - (600 // 2)
        self.window.geometry(f"900x600+{x}+{y}")
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.window, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # شريط البحث والأدوات
        self.create_toolbar(main_frame)
        
        # منطقة المحتوى الرئيسي
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))
        
        # قائمة الموردين (الجانب الأيسر)
        self.create_suppliers_list(content_frame)
        
        # تفاصيل المورد (الجانب الأيمن)
        self.create_supplier_details(content_frame)
        
        # أزرار التحكم
        self.create_control_buttons(main_frame)
        
    def create_toolbar(self, parent):
        """إنشاء شريط الأدوات"""
        toolbar = ttk.Frame(parent)
        toolbar.pack(fill=tk.X, pady=(0, 10))
        
        # البحث
        ttk.Label(toolbar, text="البحث:").pack(side=tk.LEFT)
        self.search_var = tk.StringVar()
        search_entry = ttk.Entry(toolbar, textvariable=self.search_var, width=30)
        search_entry.pack(side=tk.LEFT, padx=(5, 10))
        search_entry.bind('<KeyRelease>', self.search_suppliers)
        self.search_entry = search_entry
        
        ttk.Button(toolbar, text="بحث", command=self.search_suppliers).pack(side=tk.LEFT, padx=(0, 20))
        
        # أزرار سريعة
        ttk.Button(toolbar, text="مورد جديد", command=self.new_supplier).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="تحديث", command=self.load_suppliers).pack(side=tk.LEFT, padx=(0, 5))
        
    def create_suppliers_list(self, parent):
        """إنشاء قائمة الموردين"""
        # إطار قائمة الموردين
        list_frame = ttk.LabelFrame(parent, text="قائمة الموردين", padding="5")
        list_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        # جدول الموردين
        columns = ('الاسم', 'الهاتف', 'الرصيد')
        self.suppliers_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)
        
        # تعريف الأعمدة
        self.suppliers_tree.heading('الاسم', text='الاسم')
        self.suppliers_tree.heading('الهاتف', text='الهاتف')
        self.suppliers_tree.heading('الرصيد', text='الرصيد')
        
        self.suppliers_tree.column('الاسم', width=200, anchor=tk.W)
        self.suppliers_tree.column('الهاتف', width=120, anchor=tk.CENTER)
        self.suppliers_tree.column('الرصيد', width=100, anchor=tk.E)
        
        # شريط التمرير
        scrollbar_list = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.suppliers_tree.yview)
        self.suppliers_tree.configure(yscrollcommand=scrollbar_list.set)
        
        # تخطيط الجدول وشريط التمرير
        self.suppliers_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_list.pack(side=tk.RIGHT, fill=tk.Y)
        
        # ربط الأحداث
        self.suppliers_tree.bind('<<TreeviewSelect>>', self.on_supplier_select)
        self.suppliers_tree.bind('<Double-1>', self.edit_supplier)
        
    def create_supplier_details(self, parent):
        """إنشاء منطقة تفاصيل المورد"""
        # إطار التفاصيل
        details_frame = ttk.LabelFrame(parent, text="تفاصيل المورد", padding="10")
        details_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # الاسم
        ttk.Label(details_frame, text="الاسم:").grid(row=0, column=0, sticky=tk.W, pady=(0, 10))
        self.name_var = tk.StringVar()
        ttk.Entry(details_frame, textvariable=self.name_var, width=30).grid(row=0, column=1, sticky=tk.W+tk.E, pady=(0, 10))
        
        # الهاتف
        ttk.Label(details_frame, text="الهاتف:").grid(row=1, column=0, sticky=tk.W, pady=(0, 10))
        self.phone_var = tk.StringVar()
        ttk.Entry(details_frame, textvariable=self.phone_var, width=30).grid(row=1, column=1, sticky=tk.W+tk.E, pady=(0, 10))
        
        # البريد الإلكتروني
        ttk.Label(details_frame, text="البريد الإلكتروني:").grid(row=2, column=0, sticky=tk.W, pady=(0, 10))
        self.email_var = tk.StringVar()
        ttk.Entry(details_frame, textvariable=self.email_var, width=30).grid(row=2, column=1, sticky=tk.W+tk.E, pady=(0, 10))
        
        # العنوان
        ttk.Label(details_frame, text="العنوان:").grid(row=3, column=0, sticky=tk.W+tk.N, pady=(0, 10))
        address_text = tk.Text(details_frame, width=30, height=3)
        address_text.grid(row=3, column=1, sticky=tk.W+tk.E, pady=(0, 10))
        self.address_text = address_text
        
        # الرصيد
        ttk.Label(details_frame, text="الرصيد:").grid(row=4, column=0, sticky=tk.W, pady=(0, 10))
        self.balance_var = tk.StringVar()
        ttk.Entry(details_frame, textvariable=self.balance_var, width=30).grid(row=4, column=1, sticky=tk.W+tk.E, pady=(0, 10))
        
        # الملاحظات
        ttk.Label(details_frame, text="الملاحظات:").grid(row=5, column=0, sticky=tk.W+tk.N, pady=(0, 10))
        notes_text = tk.Text(details_frame, width=30, height=4)
        notes_text.grid(row=5, column=1, sticky=tk.W+tk.E, pady=(0, 10))
        self.notes_text = notes_text
        
        # تكوين الأعمدة للتمدد
        details_frame.columnconfigure(1, weight=1)
        
        # أزرار العمليات
        buttons_frame = ttk.Frame(details_frame)
        buttons_frame.grid(row=6, column=0, columnspan=2, pady=(20, 0), sticky=tk.W+tk.E)
        
        ttk.Button(buttons_frame, text="حفظ", command=self.save_supplier).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(buttons_frame, text="حذف", command=self.delete_supplier).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(buttons_frame, text="مسح", command=self.clear_form).pack(side=tk.LEFT, padx=(0, 5))
        
    def create_control_buttons(self, parent):
        """إنشاء أزرار التحكم"""
        buttons_frame = ttk.Frame(parent)
        buttons_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(buttons_frame, text="إغلاق", command=self.close_window).pack(side=tk.RIGHT)
        ttk.Button(buttons_frame, text="تقرير الموردين", command=self.suppliers_report).pack(side=tk.RIGHT, padx=(0, 10))
        
    def setup_keyboard_shortcuts(self):
        """إعداد اختصارات لوحة المفاتيح"""
        self.window.bind('<F1>', lambda e: self.new_supplier())
        self.window.bind('<F2>', lambda e: self.save_supplier())
        self.window.bind('<F3>', lambda e: self.delete_supplier())
        self.window.bind('<F5>', lambda e: self.load_suppliers())
        self.window.bind('<Escape>', lambda e: self.close_window())
        self.window.bind('<Control-f>', lambda e: self.search_entry.focus())
        
    def load_suppliers(self):
        """تحميل الموردين من قاعدة البيانات"""
        try:
            # مسح البيانات الحالية
            for item in self.suppliers_tree.get_children():
                self.suppliers_tree.delete(item)
                
            # تحميل الموردين
            self.suppliers = Supplier.get_all(self.db_manager)
            
            # إضافة الموردين للجدول
            for supplier in self.suppliers:
                self.suppliers_tree.insert('', tk.END, values=(
                    supplier.name,
                    supplier.phone,
                    f"{supplier.balance:.2f}"
                ))
                
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل الموردين: {str(e)}")
            
    def search_suppliers(self, event=None):
        """البحث عن الموردين"""
        search_term = self.search_var.get().strip()
        
        try:
            # مسح البيانات الحالية
            for item in self.suppliers_tree.get_children():
                self.suppliers_tree.delete(item)
                
            if search_term:
                # البحث (مماثل لنموذج العملاء)
                suppliers = [s for s in Supplier.get_all(self.db_manager) 
                           if search_term.lower() in s.name.lower() or 
                              search_term.lower() in s.phone.lower()]
            else:
                # عرض جميع الموردين
                suppliers = Supplier.get_all(self.db_manager)
                
            self.suppliers = suppliers
            
            # إضافة النتائج للجدول
            for supplier in suppliers:
                self.suppliers_tree.insert('', tk.END, values=(
                    supplier.name,
                    supplier.phone,
                    f"{supplier.balance:.2f}"
                ))
                
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في البحث: {str(e)}")
            
    def on_supplier_select(self, event):
        """عند اختيار مورد من القائمة"""
        selection = self.suppliers_tree.selection()
        if selection:
            item = self.suppliers_tree.item(selection[0])
            supplier_name = item['values'][0]
            
            # البحث عن المورد في القائمة
            for supplier in self.suppliers:
                if supplier.name == supplier_name:
                    self.selected_supplier = supplier
                    self.load_supplier_details(supplier)
                    break
                    
    def load_supplier_details(self, supplier):
        """تحميل تفاصيل المورد في النموذج"""
        self.name_var.set(supplier.name)
        self.phone_var.set(supplier.phone)
        self.email_var.set(supplier.email)
        self.balance_var.set(str(supplier.balance))
        
        # العنوان
        self.address_text.delete(1.0, tk.END)
        self.address_text.insert(1.0, supplier.address)
        
        # الملاحظات
        self.notes_text.delete(1.0, tk.END)
        self.notes_text.insert(1.0, supplier.notes)
        
    def clear_form(self):
        """مسح النموذج"""
        self.selected_supplier = None
        self.name_var.set('')
        self.phone_var.set('')
        self.email_var.set('')
        self.balance_var.set('0')
        self.address_text.delete(1.0, tk.END)
        self.notes_text.delete(1.0, tk.END)
        
    def new_supplier(self):
        """مورد جديد"""
        self.clear_form()
        
    def save_supplier(self):
        """حفظ المورد"""
        try:
            name = self.name_var.get().strip()
            if not name:
                messagebox.showwarning("تحذير", "يرجى إدخال اسم المورد")
                return
                
            phone = self.phone_var.get().strip()
            email = self.email_var.get().strip()
            address = self.address_text.get(1.0, tk.END).strip()
            notes = self.notes_text.get(1.0, tk.END).strip()
            
            try:
                balance = float(self.balance_var.get() or 0)
            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال رصيد صحيح")
                return
                
            if self.selected_supplier:
                # تحديث مورد موجود
                self.selected_supplier.name = name
                self.selected_supplier.phone = phone
                self.selected_supplier.email = email
                self.selected_supplier.address = address
                self.selected_supplier.balance = balance
                self.selected_supplier.notes = notes
                self.selected_supplier.save()
                messagebox.showinfo("نجح", "تم تحديث المورد بنجاح")
            else:
                # إضافة مورد جديد
                supplier = Supplier(self.db_manager, name=name, phone=phone, 
                                  email=email, address=address, balance=balance, notes=notes)
                supplier.save()
                messagebox.showinfo("نجح", "تم إضافة المورد بنجاح")
                
            # تحديث القائمة
            self.load_suppliers()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في حفظ المورد: {str(e)}")
            
    def delete_supplier(self):
        """حذف المورد"""
        if not self.selected_supplier:
            messagebox.showwarning("تحذير", "يرجى اختيار مورد للحذف")
            return
            
        if messagebox.askyesno("تأكيد الحذف", 
                              f"هل تريد حذف المورد '{self.selected_supplier.name}'؟\nهذا الإجراء لا يمكن التراجع عنه"):
            try:
                self.selected_supplier.delete()
                messagebox.showinfo("نجح", "تم حذف المورد بنجاح")
                self.clear_form()
                self.load_suppliers()
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في حذف المورد: {str(e)}")
                
    def edit_supplier(self, event):
        """تعديل المورد (عند النقر المزدوج)"""
        self.on_supplier_select(event)
        
    def suppliers_report(self):
        """تقرير الموردين"""
        messagebox.showinfo("قريباً", "سيتم تطوير تقرير الموردين قريباً")
        
    def close_window(self):
        """إغلاق النافذة"""
        self.window.destroy()
