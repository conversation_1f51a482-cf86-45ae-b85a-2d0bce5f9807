#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة إدارة العملاء
Customers Management Window
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# إضافة مسار المجلدات للاستيراد
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'database'))

from database.models import Customer

class CustomersWindow:
    """فئة نافذة إدارة العملاء"""
    
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.window = tk.Toplevel(parent)
        self.customers = []
        self.selected_customer = None
        
        self.setup_window()
        self.setup_ui()
        self.load_customers()
        self.setup_keyboard_shortcuts()
        
    def setup_window(self):
        """إعداد النافذة"""
        self.window.title("إدارة العملاء")
        self.window.geometry("900x600")
        self.window.resizable(True, True)
        
        # جعل النافذة في المقدمة
        self.window.transient(self.parent)
        self.window.grab_set()
        
        # توسيط النافذة
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (900 // 2)
        y = (self.window.winfo_screenheight() // 2) - (600 // 2)
        self.window.geometry(f"900x600+{x}+{y}")
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.window, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # شريط البحث والأدوات
        self.create_toolbar(main_frame)
        
        # منطقة المحتوى الرئيسي
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))
        
        # قائمة العملاء (الجانب الأيسر)
        self.create_customers_list(content_frame)
        
        # تفاصيل العميل (الجانب الأيمن)
        self.create_customer_details(content_frame)
        
        # أزرار التحكم
        self.create_control_buttons(main_frame)
        
    def create_toolbar(self, parent):
        """إنشاء شريط الأدوات"""
        toolbar = ttk.Frame(parent)
        toolbar.pack(fill=tk.X, pady=(0, 10))
        
        # البحث
        ttk.Label(toolbar, text="البحث:").pack(side=tk.LEFT)
        self.search_var = tk.StringVar()
        search_entry = ttk.Entry(toolbar, textvariable=self.search_var, width=30)
        search_entry.pack(side=tk.LEFT, padx=(5, 10))
        search_entry.bind('<KeyRelease>', self.search_customers)
        
        ttk.Button(toolbar, text="بحث", command=self.search_customers).pack(side=tk.LEFT, padx=(0, 20))
        
        # أزرار سريعة
        ttk.Button(toolbar, text="عميل جديد", command=self.new_customer).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="تحديث", command=self.load_customers).pack(side=tk.LEFT, padx=(0, 5))
        
    def create_customers_list(self, parent):
        """إنشاء قائمة العملاء"""
        # إطار قائمة العملاء
        list_frame = ttk.LabelFrame(parent, text="قائمة العملاء", padding="5")
        list_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        # جدول العملاء
        columns = ('الاسم', 'الهاتف', 'الرصيد')
        self.customers_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)
        
        # تعريف الأعمدة
        self.customers_tree.heading('الاسم', text='الاسم')
        self.customers_tree.heading('الهاتف', text='الهاتف')
        self.customers_tree.heading('الرصيد', text='الرصيد')
        
        self.customers_tree.column('الاسم', width=200, anchor=tk.W)
        self.customers_tree.column('الهاتف', width=120, anchor=tk.CENTER)
        self.customers_tree.column('الرصيد', width=100, anchor=tk.E)
        
        # شريط التمرير
        scrollbar_list = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.customers_tree.yview)
        self.customers_tree.configure(yscrollcommand=scrollbar_list.set)
        
        # تخطيط الجدول وشريط التمرير
        self.customers_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_list.pack(side=tk.RIGHT, fill=tk.Y)
        
        # ربط الأحداث
        self.customers_tree.bind('<<TreeviewSelect>>', self.on_customer_select)
        self.customers_tree.bind('<Double-1>', self.edit_customer)
        
    def create_customer_details(self, parent):
        """إنشاء منطقة تفاصيل العميل"""
        # إطار التفاصيل
        details_frame = ttk.LabelFrame(parent, text="تفاصيل العميل", padding="10")
        details_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # الاسم
        ttk.Label(details_frame, text="الاسم:").grid(row=0, column=0, sticky=tk.W, pady=(0, 10))
        self.name_var = tk.StringVar()
        ttk.Entry(details_frame, textvariable=self.name_var, width=30).grid(row=0, column=1, sticky=tk.W+tk.E, pady=(0, 10))
        
        # الهاتف
        ttk.Label(details_frame, text="الهاتف:").grid(row=1, column=0, sticky=tk.W, pady=(0, 10))
        self.phone_var = tk.StringVar()
        ttk.Entry(details_frame, textvariable=self.phone_var, width=30).grid(row=1, column=1, sticky=tk.W+tk.E, pady=(0, 10))
        
        # البريد الإلكتروني
        ttk.Label(details_frame, text="البريد الإلكتروني:").grid(row=2, column=0, sticky=tk.W, pady=(0, 10))
        self.email_var = tk.StringVar()
        ttk.Entry(details_frame, textvariable=self.email_var, width=30).grid(row=2, column=1, sticky=tk.W+tk.E, pady=(0, 10))
        
        # العنوان
        ttk.Label(details_frame, text="العنوان:").grid(row=3, column=0, sticky=tk.W+tk.N, pady=(0, 10))
        self.address_var = tk.StringVar()
        address_text = tk.Text(details_frame, width=30, height=3)
        address_text.grid(row=3, column=1, sticky=tk.W+tk.E, pady=(0, 10))
        self.address_text = address_text
        
        # الرصيد
        ttk.Label(details_frame, text="الرصيد:").grid(row=4, column=0, sticky=tk.W, pady=(0, 10))
        self.balance_var = tk.StringVar()
        balance_entry = ttk.Entry(details_frame, textvariable=self.balance_var, width=30)
        balance_entry.grid(row=4, column=1, sticky=tk.W+tk.E, pady=(0, 10))
        
        # الملاحظات
        ttk.Label(details_frame, text="الملاحظات:").grid(row=5, column=0, sticky=tk.W+tk.N, pady=(0, 10))
        notes_text = tk.Text(details_frame, width=30, height=4)
        notes_text.grid(row=5, column=1, sticky=tk.W+tk.E, pady=(0, 10))
        self.notes_text = notes_text
        
        # تكوين الأعمدة للتمدد
        details_frame.columnconfigure(1, weight=1)
        
        # أزرار العمليات
        buttons_frame = ttk.Frame(details_frame)
        buttons_frame.grid(row=6, column=0, columnspan=2, pady=(20, 0), sticky=tk.W+tk.E)
        
        ttk.Button(buttons_frame, text="حفظ", command=self.save_customer).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(buttons_frame, text="حذف", command=self.delete_customer).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(buttons_frame, text="مسح", command=self.clear_form).pack(side=tk.LEFT, padx=(0, 5))
        
    def create_control_buttons(self, parent):
        """إنشاء أزرار التحكم"""
        buttons_frame = ttk.Frame(parent)
        buttons_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(buttons_frame, text="إغلاق", command=self.close_window).pack(side=tk.RIGHT)
        ttk.Button(buttons_frame, text="تقرير العملاء", command=self.customers_report).pack(side=tk.RIGHT, padx=(0, 10))
        
    def setup_keyboard_shortcuts(self):
        """إعداد اختصارات لوحة المفاتيح"""
        self.window.bind('<F1>', lambda e: self.new_customer())
        self.window.bind('<F2>', lambda e: self.save_customer())
        self.window.bind('<F3>', lambda e: self.delete_customer())
        self.window.bind('<F5>', lambda e: self.load_customers())
        self.window.bind('<Escape>', lambda e: self.close_window())
        self.window.bind('<Control-f>', lambda e: self.search_entry.focus())
        
    def load_customers(self):
        """تحميل العملاء من قاعدة البيانات"""
        try:
            # مسح البيانات الحالية
            for item in self.customers_tree.get_children():
                self.customers_tree.delete(item)
                
            # تحميل العملاء
            self.customers = Customer.get_all(self.db_manager)
            
            # إضافة العملاء للجدول
            for customer in self.customers:
                self.customers_tree.insert('', tk.END, values=(
                    customer.name,
                    customer.phone,
                    f"{customer.balance:.2f}"
                ))
                
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل العملاء: {str(e)}")
            
    def search_customers(self, event=None):
        """البحث عن العملاء"""
        search_term = self.search_var.get().strip()
        
        try:
            # مسح البيانات الحالية
            for item in self.customers_tree.get_children():
                self.customers_tree.delete(item)
                
            if search_term:
                # البحث
                customers = Customer.search(self.db_manager, search_term)
            else:
                # عرض جميع العملاء
                customers = Customer.get_all(self.db_manager)
                
            self.customers = customers
            
            # إضافة النتائج للجدول
            for customer in customers:
                self.customers_tree.insert('', tk.END, values=(
                    customer.name,
                    customer.phone,
                    f"{customer.balance:.2f}"
                ))
                
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في البحث: {str(e)}")
            
    def on_customer_select(self, event):
        """عند اختيار عميل من القائمة"""
        selection = self.customers_tree.selection()
        if selection:
            item = self.customers_tree.item(selection[0])
            customer_name = item['values'][0]
            
            # البحث عن العميل في القائمة
            for customer in self.customers:
                if customer.name == customer_name:
                    self.selected_customer = customer
                    self.load_customer_details(customer)
                    break
                    
    def load_customer_details(self, customer):
        """تحميل تفاصيل العميل في النموذج"""
        self.name_var.set(customer.name)
        self.phone_var.set(customer.phone)
        self.email_var.set(customer.email)
        self.balance_var.set(str(customer.balance))
        
        # العنوان
        self.address_text.delete(1.0, tk.END)
        self.address_text.insert(1.0, customer.address)
        
        # الملاحظات
        self.notes_text.delete(1.0, tk.END)
        self.notes_text.insert(1.0, customer.notes)
        
    def clear_form(self):
        """مسح النموذج"""
        self.selected_customer = None
        self.name_var.set('')
        self.phone_var.set('')
        self.email_var.set('')
        self.balance_var.set('0')
        self.address_text.delete(1.0, tk.END)
        self.notes_text.delete(1.0, tk.END)
        
    def new_customer(self):
        """عميل جديد"""
        self.clear_form()
        
    def save_customer(self):
        """حفظ العميل"""
        try:
            name = self.name_var.get().strip()
            if not name:
                messagebox.showwarning("تحذير", "يرجى إدخال اسم العميل")
                return
                
            phone = self.phone_var.get().strip()
            email = self.email_var.get().strip()
            address = self.address_text.get(1.0, tk.END).strip()
            notes = self.notes_text.get(1.0, tk.END).strip()
            
            try:
                balance = float(self.balance_var.get() or 0)
            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال رصيد صحيح")
                return
                
            if self.selected_customer:
                # تحديث عميل موجود
                self.selected_customer.name = name
                self.selected_customer.phone = phone
                self.selected_customer.email = email
                self.selected_customer.address = address
                self.selected_customer.balance = balance
                self.selected_customer.notes = notes
                self.selected_customer.save()
                messagebox.showinfo("نجح", "تم تحديث العميل بنجاح")
            else:
                # إضافة عميل جديد
                customer = Customer(self.db_manager, name=name, phone=phone, 
                                  email=email, address=address, balance=balance, notes=notes)
                customer.save()
                messagebox.showinfo("نجح", "تم إضافة العميل بنجاح")
                
            # تحديث القائمة
            self.load_customers()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في حفظ العميل: {str(e)}")
            
    def delete_customer(self):
        """حذف العميل"""
        if not self.selected_customer:
            messagebox.showwarning("تحذير", "يرجى اختيار عميل للحذف")
            return
            
        if messagebox.askyesno("تأكيد الحذف", 
                              f"هل تريد حذف العميل '{self.selected_customer.name}'؟\nهذا الإجراء لا يمكن التراجع عنه"):
            try:
                self.selected_customer.delete()
                messagebox.showinfo("نجح", "تم حذف العميل بنجاح")
                self.clear_form()
                self.load_customers()
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في حذف العميل: {str(e)}")
                
    def edit_customer(self, event):
        """تعديل العميل (عند النقر المزدوج)"""
        self.on_customer_select(event)
        
    def customers_report(self):
        """تقرير العملاء"""
        messagebox.showinfo("قريباً", "سيتم تطوير تقرير العملاء قريباً")
        
    def close_window(self):
        """إغلاق النافذة"""
        self.window.destroy()
