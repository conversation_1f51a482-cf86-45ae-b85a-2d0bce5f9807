#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نماذج قاعدة البيانات
Database Models

يحتوي على فئات نماذج البيانات لجميع الكيانات
"""

from datetime import datetime
from typing import List, Optional, Dict, Any

class BaseModel:
    """الفئة الأساسية لجميع النماذج"""
    
    def __init__(self, db_manager):
        self.db_manager = db_manager
        
    def to_dict(self) -> Dict[str, Any]:
        """تحويل الكائن إلى قاموس"""
        return {key: value for key, value in self.__dict__.items() if not key.startswith('_')}

class Customer(BaseModel):
    """نموذج العميل"""
    
    def __init__(self, db_manager, id=None, name="", phone="", address="", email="", balance=0.0, notes=""):
        super().__init__(db_manager)
        self.id = id
        self.name = name
        self.phone = phone
        self.address = address
        self.email = email
        self.balance = balance
        self.notes = notes
        self.created_date = datetime.now().isoformat()
        
    def save(self) -> int:
        """حفظ العميل في قاعدة البيانات"""
        if self.id:
            # تحديث عميل موجود
            query = """
                UPDATE customers 
                SET name=?, phone=?, address=?, email=?, balance=?, notes=?
                WHERE id=?
            """
            params = (self.name, self.phone, self.address, self.email, self.balance, self.notes, self.id)
            self.db_manager.execute_query(query, params)
            return self.id
        else:
            # إضافة عميل جديد
            query = """
                INSERT INTO customers (name, phone, address, email, balance, notes, created_date)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """
            params = (self.name, self.phone, self.address, self.email, self.balance, self.notes, self.created_date)
            self.id = self.db_manager.execute_insert(query, params)
            return self.id
            
    @classmethod
    def get_by_id(cls, db_manager, customer_id: int):
        """الحصول على عميل بواسطة المعرف"""
        query = "SELECT * FROM customers WHERE id=?"
        result = db_manager.execute_query(query, (customer_id,))
        if result:
            row = result[0]
            return cls(db_manager, row['id'], row['name'], row['phone'], 
                      row['address'], row['email'], row['balance'], row['notes'])
        return None
        
    @classmethod
    def get_all(cls, db_manager) -> List['Customer']:
        """الحصول على جميع العملاء"""
        query = "SELECT * FROM customers ORDER BY name"
        results = db_manager.execute_query(query)
        customers = []
        for row in results:
            customer = cls(db_manager, row['id'], row['name'], row['phone'], 
                          row['address'], row['email'], row['balance'], row['notes'])
            customers.append(customer)
        return customers
        
    @classmethod
    def search(cls, db_manager, search_term: str) -> List['Customer']:
        """البحث عن العملاء"""
        query = """
            SELECT * FROM customers 
            WHERE name LIKE ? OR phone LIKE ? OR address LIKE ?
            ORDER BY name
        """
        search_pattern = f"%{search_term}%"
        results = db_manager.execute_query(query, (search_pattern, search_pattern, search_pattern))
        customers = []
        for row in results:
            customer = cls(db_manager, row['id'], row['name'], row['phone'], 
                          row['address'], row['email'], row['balance'], row['notes'])
            customers.append(customer)
        return customers
        
    def delete(self) -> bool:
        """حذف العميل"""
        if self.id:
            query = "DELETE FROM customers WHERE id=?"
            self.db_manager.execute_query(query, (self.id,))
            return True
        return False
        
    def update_balance(self, amount: float, operation: str = 'add'):
        """تحديث رصيد العميل"""
        if operation == 'add':
            self.balance += amount
        elif operation == 'subtract':
            self.balance -= amount
        elif operation == 'set':
            self.balance = amount
            
        query = "UPDATE customers SET balance=? WHERE id=?"
        self.db_manager.execute_query(query, (self.balance, self.id))

class Supplier(BaseModel):
    """نموذج المورد"""
    
    def __init__(self, db_manager, id=None, name="", phone="", address="", email="", balance=0.0, notes=""):
        super().__init__(db_manager)
        self.id = id
        self.name = name
        self.phone = phone
        self.address = address
        self.email = email
        self.balance = balance
        self.notes = notes
        self.created_date = datetime.now().isoformat()
        
    def save(self) -> int:
        """حفظ المورد في قاعدة البيانات"""
        if self.id:
            query = """
                UPDATE suppliers 
                SET name=?, phone=?, address=?, email=?, balance=?, notes=?
                WHERE id=?
            """
            params = (self.name, self.phone, self.address, self.email, self.balance, self.notes, self.id)
            self.db_manager.execute_query(query, params)
            return self.id
        else:
            query = """
                INSERT INTO suppliers (name, phone, address, email, balance, notes, created_date)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """
            params = (self.name, self.phone, self.address, self.email, self.balance, self.notes, self.created_date)
            self.id = self.db_manager.execute_insert(query, params)
            return self.id
            
    @classmethod
    def get_by_id(cls, db_manager, supplier_id: int):
        """الحصول على مورد بواسطة المعرف"""
        query = "SELECT * FROM suppliers WHERE id=?"
        result = db_manager.execute_query(query, (supplier_id,))
        if result:
            row = result[0]
            return cls(db_manager, row['id'], row['name'], row['phone'], 
                      row['address'], row['email'], row['balance'], row['notes'])
        return None
        
    @classmethod
    def get_all(cls, db_manager) -> List['Supplier']:
        """الحصول على جميع الموردين"""
        query = "SELECT * FROM suppliers ORDER BY name"
        results = db_manager.execute_query(query)
        suppliers = []
        for row in results:
            supplier = cls(db_manager, row['id'], row['name'], row['phone'], 
                          row['address'], row['email'], row['balance'], row['notes'])
            suppliers.append(supplier)
        return suppliers
        
    def delete(self) -> bool:
        """حذف المورد"""
        if self.id:
            query = "DELETE FROM suppliers WHERE id=?"
            self.db_manager.execute_query(query, (self.id,))
            return True
        return False
        
    def update_balance(self, amount: float, operation: str = 'add'):
        """تحديث رصيد المورد"""
        if operation == 'add':
            self.balance += amount
        elif operation == 'subtract':
            self.balance -= amount
        elif operation == 'set':
            self.balance = amount
            
        query = "UPDATE suppliers SET balance=? WHERE id=?"
        self.db_manager.execute_query(query, (self.balance, self.id))

class Product(BaseModel):
    """نموذج المنتج"""
    
    def __init__(self, db_manager, id=None, name="", barcode="", unit="قطعة", 
                 purchase_price=0.0, sale_price=0.0, quantity=0.0, min_quantity=0.0, 
                 category="", notes=""):
        super().__init__(db_manager)
        self.id = id
        self.name = name
        self.barcode = barcode
        self.unit = unit
        self.purchase_price = purchase_price
        self.sale_price = sale_price
        self.quantity = quantity
        self.min_quantity = min_quantity
        self.category = category
        self.notes = notes
        self.created_date = datetime.now().isoformat()
        
    def save(self) -> int:
        """حفظ المنتج في قاعدة البيانات"""
        if self.id:
            query = """
                UPDATE products 
                SET name=?, barcode=?, unit=?, purchase_price=?, sale_price=?, 
                    quantity=?, min_quantity=?, category=?, notes=?
                WHERE id=?
            """
            params = (self.name, self.barcode, self.unit, self.purchase_price, 
                     self.sale_price, self.quantity, self.min_quantity, 
                     self.category, self.notes, self.id)
            self.db_manager.execute_query(query, params)
            return self.id
        else:
            query = """
                INSERT INTO products (name, barcode, unit, purchase_price, sale_price, 
                                    quantity, min_quantity, category, notes, created_date)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            params = (self.name, self.barcode, self.unit, self.purchase_price, 
                     self.sale_price, self.quantity, self.min_quantity, 
                     self.category, self.notes, self.created_date)
            self.id = self.db_manager.execute_insert(query, params)
            return self.id
            
    @classmethod
    def get_by_id(cls, db_manager, product_id: int):
        """الحصول على منتج بواسطة المعرف"""
        query = "SELECT * FROM products WHERE id=?"
        result = db_manager.execute_query(query, (product_id,))
        if result:
            row = result[0]
            return cls(db_manager, row['id'], row['name'], row['barcode'], row['unit'],
                      row['purchase_price'], row['sale_price'], row['quantity'], 
                      row['min_quantity'], row['category'], row['notes'])
        return None
        
    @classmethod
    def get_by_barcode(cls, db_manager, barcode: str):
        """الحصول على منتج بواسطة الباركود"""
        query = "SELECT * FROM products WHERE barcode=?"
        result = db_manager.execute_query(query, (barcode,))
        if result:
            row = result[0]
            return cls(db_manager, row['id'], row['name'], row['barcode'], row['unit'],
                      row['purchase_price'], row['sale_price'], row['quantity'], 
                      row['min_quantity'], row['category'], row['notes'])
        return None
        
    @classmethod
    def get_all(cls, db_manager) -> List['Product']:
        """الحصول على جميع المنتجات"""
        query = "SELECT * FROM products ORDER BY name"
        results = db_manager.execute_query(query)
        products = []
        for row in results:
            product = cls(db_manager, row['id'], row['name'], row['barcode'], row['unit'],
                         row['purchase_price'], row['sale_price'], row['quantity'], 
                         row['min_quantity'], row['category'], row['notes'])
            products.append(product)
        return products
        
    @classmethod
    def get_low_stock(cls, db_manager) -> List['Product']:
        """الحصول على المنتجات منخفضة المخزون"""
        query = "SELECT * FROM products WHERE quantity <= min_quantity ORDER BY name"
        results = db_manager.execute_query(query)
        products = []
        for row in results:
            product = cls(db_manager, row['id'], row['name'], row['barcode'], row['unit'],
                         row['purchase_price'], row['sale_price'], row['quantity'], 
                         row['min_quantity'], row['category'], row['notes'])
            products.append(product)
        return products
        
    def update_quantity(self, quantity_change: float, operation: str = 'add'):
        """تحديث كمية المنتج"""
        if operation == 'add':
            self.quantity += quantity_change
        elif operation == 'subtract':
            self.quantity -= quantity_change
        elif operation == 'set':
            self.quantity = quantity_change
            
        query = "UPDATE products SET quantity=? WHERE id=?"
        self.db_manager.execute_query(query, (self.quantity, self.id))

    def delete(self) -> bool:
        """حذف المنتج"""
        if self.id:
            query = "DELETE FROM products WHERE id=?"
            self.db_manager.execute_query(query, (self.id,))
            return True
        return False

class SalesInvoice(BaseModel):
    """نموذج فاتورة المبيعات"""

    def __init__(self, db_manager, id=None, invoice_number="", customer_id=None,
                 invoice_date="", total_amount=0.0, discount=0.0, tax=0.0,
                 final_amount=0.0, paid_amount=0.0, remaining_amount=0.0,
                 payment_method="نقدي", notes="", status="مفتوحة"):
        super().__init__(db_manager)
        self.id = id
        self.invoice_number = invoice_number
        self.customer_id = customer_id
        self.invoice_date = invoice_date or datetime.now().isoformat()
        self.total_amount = total_amount
        self.discount = discount
        self.tax = tax
        self.final_amount = final_amount
        self.paid_amount = paid_amount
        self.remaining_amount = remaining_amount
        self.payment_method = payment_method
        self.notes = notes
        self.status = status
        self.items = []

    def save(self) -> int:
        """حفظ فاتورة المبيعات"""
        if self.id:
            # تحديث فاتورة موجودة
            query = """
                UPDATE sales_invoices
                SET customer_id=?, invoice_date=?, total_amount=?, discount=?, tax=?,
                    final_amount=?, paid_amount=?, remaining_amount=?, payment_method=?,
                    notes=?, status=?
                WHERE id=?
            """
            params = (self.customer_id, self.invoice_date, self.total_amount, self.discount,
                     self.tax, self.final_amount, self.paid_amount, self.remaining_amount,
                     self.payment_method, self.notes, self.status, self.id)
            self.db_manager.execute_query(query, params)
            return self.id
        else:
            # إضافة فاتورة جديدة
            query = """
                INSERT INTO sales_invoices (invoice_number, customer_id, invoice_date,
                                          total_amount, discount, tax, final_amount,
                                          paid_amount, remaining_amount, payment_method,
                                          notes, status)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            params = (self.invoice_number, self.customer_id, self.invoice_date,
                     self.total_amount, self.discount, self.tax, self.final_amount,
                     self.paid_amount, self.remaining_amount, self.payment_method,
                     self.notes, self.status)
            self.id = self.db_manager.execute_insert(query, params)
            return self.id

    def save_with_items(self, items_data: List[Dict]) -> int:
        """حفظ الفاتورة مع الأصناف"""
        # حفظ الفاتورة أولاً
        invoice_id = self.save()

        # حذف الأصناف القديمة إذا كانت فاتورة محدثة
        if self.id:
            self.db_manager.execute_query("DELETE FROM sales_invoice_items WHERE invoice_id=?", (invoice_id,))

        # إضافة الأصناف الجديدة
        for item in items_data:
            query = """
                INSERT INTO sales_invoice_items (invoice_id, product_id, quantity, unit_price, total_price)
                VALUES (?, ?, ?, ?, ?)
            """
            params = (invoice_id, item['product_id'], item['quantity'],
                     item['unit_price'], item['total_price'])
            self.db_manager.execute_insert(query, params)

            # تحديث كمية المنتج
            product = Product.get_by_id(self.db_manager, item['product_id'])
            if product:
                product.update_quantity(item['quantity'], 'subtract')

        return invoice_id

    @classmethod
    def get_by_id(cls, db_manager, invoice_id: int):
        """الحصول على فاتورة بواسطة المعرف"""
        query = "SELECT * FROM sales_invoices WHERE id=?"
        result = db_manager.execute_query(query, (invoice_id,))
        if result:
            row = result[0]
            invoice = cls(db_manager, row['id'], row['invoice_number'], row['customer_id'],
                         row['invoice_date'], row['total_amount'], row['discount'], row['tax'],
                         row['final_amount'], row['paid_amount'], row['remaining_amount'],
                         row['payment_method'], row['notes'], row['status'])

            # تحميل الأصناف
            items_query = """
                SELECT sii.*, p.name as product_name, p.unit
                FROM sales_invoice_items sii
                JOIN products p ON sii.product_id = p.id
                WHERE sii.invoice_id = ?
            """
            items_result = db_manager.execute_query(items_query, (invoice_id,))
            invoice.items = [dict(row) for row in items_result]

            return invoice
        return None

    @classmethod
    def get_all(cls, db_manager) -> List['SalesInvoice']:
        """الحصول على جميع فواتير المبيعات"""
        query = """
            SELECT si.*, c.name as customer_name
            FROM sales_invoices si
            LEFT JOIN customers c ON si.customer_id = c.id
            ORDER BY si.invoice_date DESC
        """
        results = db_manager.execute_query(query)
        invoices = []
        for row in results:
            invoice = cls(db_manager, row['id'], row['invoice_number'], row['customer_id'],
                         row['invoice_date'], row['total_amount'], row['discount'], row['tax'],
                         row['final_amount'], row['paid_amount'], row['remaining_amount'],
                         row['payment_method'], row['notes'], row['status'])
            invoices.append(invoice)
        return invoices
