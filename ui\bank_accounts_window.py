#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة إدارة الحسابات المصرفية
Bank Accounts Management Window
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
import sys
import os

class BankAccountsWindow:
    """فئة نافذة إدارة الحسابات المصرفية"""
    
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.window = tk.Toplevel(parent)
        self.bank_accounts = []
        self.selected_account = None
        
        self.setup_window()
        self.setup_ui()
        self.load_accounts()
        self.setup_keyboard_shortcuts()
        
    def setup_window(self):
        """إعداد النافذة"""
        self.window.title("إدارة الحسابات المصرفية")
        self.window.geometry("1000x600")
        self.window.resizable(True, True)
        
        # جعل النافذة في المقدمة
        self.window.transient(self.parent)
        self.window.grab_set()
        
        # توسيط النافذة
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (1000 // 2)
        y = (self.window.winfo_screenheight() // 2) - (600 // 2)
        self.window.geometry(f"1000x600+{x}+{y}")
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.window, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # شريط الأدوات
        self.create_toolbar(main_frame)
        
        # منطقة المحتوى الرئيسي
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))
        
        # قائمة الحسابات (الجانب الأيسر)
        self.create_accounts_list(content_frame)
        
        # تفاصيل الحساب (الجانب الأيمن)
        self.create_account_details(content_frame)
        
        # أزرار التحكم
        self.create_control_buttons(main_frame)
        
    def create_toolbar(self, parent):
        """إنشاء شريط الأدوات"""
        toolbar = ttk.Frame(parent)
        toolbar.pack(fill=tk.X, pady=(0, 10))
        
        # أزرار سريعة
        ttk.Button(toolbar, text="حساب جديد", command=self.new_account).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(toolbar, text="تحديث", command=self.load_accounts).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(toolbar, text="إيداع", command=self.deposit_money).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(toolbar, text="سحب", command=self.withdraw_money).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(toolbar, text="تحويل", command=self.transfer_money).pack(side=tk.LEFT, padx=(0, 10))
        
    def create_accounts_list(self, parent):
        """إنشاء قائمة الحسابات"""
        # إطار قائمة الحسابات
        list_frame = ttk.LabelFrame(parent, text="الحسابات المصرفية", padding="5")
        list_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        # جدول الحسابات
        columns = ('اسم الحساب', 'البنك', 'رقم الحساب', 'الرصيد', 'تاريخ الإنشاء')
        self.accounts_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)
        
        # تعريف الأعمدة
        self.accounts_tree.heading('اسم الحساب', text='اسم الحساب')
        self.accounts_tree.heading('البنك', text='البنك')
        self.accounts_tree.heading('رقم الحساب', text='رقم الحساب')
        self.accounts_tree.heading('الرصيد', text='الرصيد')
        self.accounts_tree.heading('تاريخ الإنشاء', text='تاريخ الإنشاء')
        
        self.accounts_tree.column('اسم الحساب', width=150, anchor=tk.W)
        self.accounts_tree.column('البنك', width=120, anchor=tk.W)
        self.accounts_tree.column('رقم الحساب', width=120, anchor=tk.CENTER)
        self.accounts_tree.column('الرصيد', width=100, anchor=tk.E)
        self.accounts_tree.column('تاريخ الإنشاء', width=100, anchor=tk.CENTER)
        
        # شريط التمرير
        scrollbar_list = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.accounts_tree.yview)
        self.accounts_tree.configure(yscrollcommand=scrollbar_list.set)
        
        # تخطيط الجدول وشريط التمرير
        self.accounts_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_list.pack(side=tk.RIGHT, fill=tk.Y)
        
        # ربط الأحداث
        self.accounts_tree.bind('<<TreeviewSelect>>', self.on_account_select)
        self.accounts_tree.bind('<Double-1>', self.edit_account)
        
    def create_account_details(self, parent):
        """إنشاء منطقة تفاصيل الحساب"""
        # إطار التفاصيل
        details_frame = ttk.LabelFrame(parent, text="تفاصيل الحساب", padding="10")
        details_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # اسم الحساب
        ttk.Label(details_frame, text="اسم الحساب:").grid(row=0, column=0, sticky=tk.W, pady=(0, 10))
        self.account_name_var = tk.StringVar()
        ttk.Entry(details_frame, textvariable=self.account_name_var, width=30).grid(row=0, column=1, sticky=tk.W+tk.E, pady=(0, 10))
        
        # اسم البنك
        ttk.Label(details_frame, text="اسم البنك:").grid(row=1, column=0, sticky=tk.W, pady=(0, 10))
        self.bank_name_var = tk.StringVar()
        bank_combo = ttk.Combobox(details_frame, textvariable=self.bank_name_var, width=27)
        bank_combo['values'] = ('البنك الأهلي المصري', 'بنك مصر', 'البنك التجاري الدولي', 'بنك القاهرة', 
                               'بنك الإسكندرية', 'البنك العربي الأفريقي', 'بنك فيصل الإسلامي', 'بنك أبوظبي الوطني')
        bank_combo.grid(row=1, column=1, sticky=tk.W+tk.E, pady=(0, 10))
        
        # رقم الحساب
        ttk.Label(details_frame, text="رقم الحساب:").grid(row=2, column=0, sticky=tk.W, pady=(0, 10))
        self.account_number_var = tk.StringVar()
        ttk.Entry(details_frame, textvariable=self.account_number_var, width=30).grid(row=2, column=1, sticky=tk.W+tk.E, pady=(0, 10))
        
        # الرصيد الحالي
        ttk.Label(details_frame, text="الرصيد الحالي:").grid(row=3, column=0, sticky=tk.W, pady=(0, 10))
        self.balance_var = tk.StringVar()
        balance_entry = ttk.Entry(details_frame, textvariable=self.balance_var, width=30, state='readonly')
        balance_entry.grid(row=3, column=1, sticky=tk.W+tk.E, pady=(0, 10))
        
        # الرصيد الابتدائي (للحسابات الجديدة)
        ttk.Label(details_frame, text="الرصيد الابتدائي:").grid(row=4, column=0, sticky=tk.W, pady=(0, 10))
        self.initial_balance_var = tk.StringVar(value="0")
        ttk.Entry(details_frame, textvariable=self.initial_balance_var, width=30).grid(row=4, column=1, sticky=tk.W+tk.E, pady=(0, 10))
        
        # الملاحظات
        ttk.Label(details_frame, text="الملاحظات:").grid(row=5, column=0, sticky=tk.W+tk.N, pady=(0, 10))
        self.notes_text = tk.Text(details_frame, width=30, height=4)
        self.notes_text.grid(row=5, column=1, sticky=tk.W+tk.E, pady=(0, 10))
        
        # تكوين الأعمدة للتمدد
        details_frame.columnconfigure(1, weight=1)
        
        # أزرار العمليات
        buttons_frame = ttk.Frame(details_frame)
        buttons_frame.grid(row=6, column=0, columnspan=2, pady=(20, 0), sticky=tk.W+tk.E)
        
        ttk.Button(buttons_frame, text="حفظ", command=self.save_account).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(buttons_frame, text="حذف", command=self.delete_account).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(buttons_frame, text="مسح", command=self.clear_form).pack(side=tk.LEFT, padx=(0, 5))
        
        # قسم العمليات المصرفية
        operations_frame = ttk.LabelFrame(details_frame, text="العمليات المصرفية", padding="10")
        operations_frame.grid(row=7, column=0, columnspan=2, pady=(20, 0), sticky=tk.W+tk.E+tk.N+tk.S)
        
        # مبلغ العملية
        ttk.Label(operations_frame, text="المبلغ:").grid(row=0, column=0, sticky=tk.W, pady=(0, 10))
        self.operation_amount_var = tk.StringVar()
        ttk.Entry(operations_frame, textvariable=self.operation_amount_var, width=20).grid(row=0, column=1, sticky=tk.W, pady=(0, 10))
        
        # وصف العملية
        ttk.Label(operations_frame, text="الوصف:").grid(row=1, column=0, sticky=tk.W, pady=(0, 10))
        self.operation_desc_var = tk.StringVar()
        ttk.Entry(operations_frame, textvariable=self.operation_desc_var, width=20).grid(row=1, column=1, sticky=tk.W, pady=(0, 10))
        
        # أزرار العمليات
        ops_buttons = ttk.Frame(operations_frame)
        ops_buttons.grid(row=2, column=0, columnspan=2, pady=(10, 0))
        
        ttk.Button(ops_buttons, text="إيداع", command=self.deposit_money).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(ops_buttons, text="سحب", command=self.withdraw_money).pack(side=tk.LEFT, padx=(0, 5))
        
    def create_control_buttons(self, parent):
        """إنشاء أزرار التحكم"""
        buttons_frame = ttk.Frame(parent)
        buttons_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(buttons_frame, text="إغلاق", command=self.close_window).pack(side=tk.RIGHT)
        ttk.Button(buttons_frame, text="كشف حساب", command=self.account_statement).pack(side=tk.RIGHT, padx=(0, 10))
        ttk.Button(buttons_frame, text="تقرير الحسابات", command=self.accounts_report).pack(side=tk.RIGHT, padx=(0, 10))
        
    def setup_keyboard_shortcuts(self):
        """إعداد اختصارات لوحة المفاتيح"""
        self.window.bind('<F1>', lambda e: self.new_account())
        self.window.bind('<F2>', lambda e: self.save_account())
        self.window.bind('<F3>', lambda e: self.delete_account())
        self.window.bind('<F5>', lambda e: self.load_accounts())
        self.window.bind('<Escape>', lambda e: self.close_window())
        
    def load_accounts(self):
        """تحميل الحسابات من قاعدة البيانات"""
        try:
            # مسح البيانات الحالية
            for item in self.accounts_tree.get_children():
                self.accounts_tree.delete(item)
                
            # تحميل الحسابات
            query = "SELECT * FROM bank_accounts ORDER BY account_name"
            results = self.db_manager.execute_query(query)
            
            self.bank_accounts = []
            for row in results:
                account_data = dict(row)
                self.bank_accounts.append(account_data)
                
                # إضافة للجدول
                self.accounts_tree.insert('', tk.END, values=(
                    account_data['account_name'],
                    account_data['bank_name'],
                    account_data['account_number'],
                    f"{account_data['balance']:.2f}",
                    account_data['created_date'][:10] if account_data['created_date'] else ''
                ))
                
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل الحسابات: {str(e)}")
            
    def on_account_select(self, event):
        """عند اختيار حساب من القائمة"""
        selection = self.accounts_tree.selection()
        if selection:
            item = self.accounts_tree.item(selection[0])
            account_name = item['values'][0]
            
            # البحث عن الحساب في القائمة
            for account in self.bank_accounts:
                if account['account_name'] == account_name:
                    self.selected_account = account
                    self.load_account_details(account)
                    break
                    
    def load_account_details(self, account):
        """تحميل تفاصيل الحساب في النموذج"""
        self.account_name_var.set(account['account_name'])
        self.bank_name_var.set(account['bank_name'])
        self.account_number_var.set(account['account_number'])
        self.balance_var.set(f"{account['balance']:.2f}")
        self.initial_balance_var.set("0")  # للحسابات الموجودة
        
        # الملاحظات
        self.notes_text.delete(1.0, tk.END)
        if account['notes']:
            self.notes_text.insert(1.0, account['notes'])
        
    def clear_form(self):
        """مسح النموذج"""
        self.selected_account = None
        self.account_name_var.set('')
        self.bank_name_var.set('')
        self.account_number_var.set('')
        self.balance_var.set('0.00')
        self.initial_balance_var.set('0')
        self.notes_text.delete(1.0, tk.END)
        self.operation_amount_var.set('')
        self.operation_desc_var.set('')
        
    def new_account(self):
        """حساب جديد"""
        self.clear_form()
        
    def save_account(self):
        """حفظ الحساب"""
        try:
            account_name = self.account_name_var.get().strip()
            bank_name = self.bank_name_var.get().strip()
            account_number = self.account_number_var.get().strip()
            notes = self.notes_text.get(1.0, tk.END).strip()
            
            if not account_name or not bank_name:
                messagebox.showwarning("تحذير", "يرجى إدخال اسم الحساب واسم البنك")
                return
                
            try:
                initial_balance = float(self.initial_balance_var.get() or 0)
            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال رصيد صحيح")
                return
                
            if self.selected_account:
                # تحديث حساب موجود
                query = """
                    UPDATE bank_accounts 
                    SET account_name=?, bank_name=?, account_number=?, notes=?
                    WHERE id=?
                """
                params = (account_name, bank_name, account_number, notes, self.selected_account['id'])
                self.db_manager.execute_query(query, params)
                messagebox.showinfo("نجح", "تم تحديث الحساب بنجاح")
            else:
                # إضافة حساب جديد
                query = """
                    INSERT INTO bank_accounts (account_name, bank_name, account_number, balance, notes, created_date)
                    VALUES (?, ?, ?, ?, ?, ?)
                """
                params = (account_name, bank_name, account_number, initial_balance, notes, datetime.now().isoformat())
                account_id = self.db_manager.execute_insert(query, params)
                
                # إضافة حركة الرصيد الابتدائي إذا كان أكبر من صفر
                if initial_balance > 0:
                    trans_query = """
                        INSERT INTO cash_bank_transactions (transaction_type, account_id, amount, description, transaction_date)
                        VALUES (?, ?, ?, ?, ?)
                    """
                    trans_params = ('bank_in', account_id, initial_balance, 'رصيد ابتدائي', datetime.now().isoformat())
                    self.db_manager.execute_insert(trans_query, trans_params)
                
                messagebox.showinfo("نجح", "تم إضافة الحساب بنجاح")
                
            # تحديث القائمة
            self.load_accounts()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في حفظ الحساب: {str(e)}")
            
    def delete_account(self):
        """حذف الحساب"""
        if not self.selected_account:
            messagebox.showwarning("تحذير", "يرجى اختيار حساب للحذف")
            return
            
        if messagebox.askyesno("تأكيد الحذف", 
                              f"هل تريد حذف الحساب '{self.selected_account['account_name']}'؟\nهذا الإجراء لا يمكن التراجع عنه"):
            try:
                # حذف الحركات المرتبطة بالحساب
                self.db_manager.execute_query("DELETE FROM cash_bank_transactions WHERE account_id=?", 
                                            (self.selected_account['id'],))
                
                # حذف الحساب
                self.db_manager.execute_query("DELETE FROM bank_accounts WHERE id=?", 
                                            (self.selected_account['id'],))
                
                messagebox.showinfo("نجح", "تم حذف الحساب بنجاح")
                self.clear_form()
                self.load_accounts()
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في حذف الحساب: {str(e)}")
                
    def deposit_money(self):
        """إيداع مبلغ في الحساب"""
        if not self.selected_account:
            messagebox.showwarning("تحذير", "يرجى اختيار حساب أولاً")
            return
            
        try:
            amount = float(self.operation_amount_var.get() or 0)
            description = self.operation_desc_var.get().strip()
            
            if amount <= 0:
                messagebox.showwarning("تحذير", "يرجى إدخال مبلغ صحيح")
                return
                
            if not description:
                description = "إيداع نقدي"
                
            # تحديث رصيد الحساب
            new_balance = self.selected_account['balance'] + amount
            update_query = "UPDATE bank_accounts SET balance=? WHERE id=?"
            self.db_manager.execute_query(update_query, (new_balance, self.selected_account['id']))
            
            # إضافة حركة
            trans_query = """
                INSERT INTO cash_bank_transactions (transaction_type, account_id, amount, description, transaction_date)
                VALUES (?, ?, ?, ?, ?)
            """
            trans_params = ('bank_in', self.selected_account['id'], amount, description, datetime.now().isoformat())
            self.db_manager.execute_insert(trans_query, trans_params)
            
            messagebox.showinfo("نجح", f"تم إيداع {amount:.2f} ج.م بنجاح")
            
            # تحديث البيانات
            self.load_accounts()
            self.operation_amount_var.set('')
            self.operation_desc_var.set('')
            
        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال مبلغ صحيح")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في الإيداع: {str(e)}")
            
    def withdraw_money(self):
        """سحب مبلغ من الحساب"""
        if not self.selected_account:
            messagebox.showwarning("تحذير", "يرجى اختيار حساب أولاً")
            return
            
        try:
            amount = float(self.operation_amount_var.get() or 0)
            description = self.operation_desc_var.get().strip()
            
            if amount <= 0:
                messagebox.showwarning("تحذير", "يرجى إدخال مبلغ صحيح")
                return
                
            if amount > self.selected_account['balance']:
                messagebox.showwarning("تحذير", "المبلغ أكبر من الرصيد المتاح")
                return
                
            if not description:
                description = "سحب نقدي"
                
            # تحديث رصيد الحساب
            new_balance = self.selected_account['balance'] - amount
            update_query = "UPDATE bank_accounts SET balance=? WHERE id=?"
            self.db_manager.execute_query(update_query, (new_balance, self.selected_account['id']))
            
            # إضافة حركة
            trans_query = """
                INSERT INTO cash_bank_transactions (transaction_type, account_id, amount, description, transaction_date)
                VALUES (?, ?, ?, ?, ?)
            """
            trans_params = ('bank_out', self.selected_account['id'], amount, description, datetime.now().isoformat())
            self.db_manager.execute_insert(trans_query, trans_params)
            
            messagebox.showinfo("نجح", f"تم سحب {amount:.2f} ج.م بنجاح")
            
            # تحديث البيانات
            self.load_accounts()
            self.operation_amount_var.set('')
            self.operation_desc_var.set('')
            
        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال مبلغ صحيح")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في السحب: {str(e)}")
            
    def transfer_money(self):
        """تحويل مبلغ بين الحسابات"""
        messagebox.showinfo("قريباً", "سيتم تطوير التحويلات بين الحسابات قريباً")
        
    def edit_account(self, event):
        """تعديل الحساب (عند النقر المزدوج)"""
        self.on_account_select(event)
        
    def account_statement(self):
        """كشف حساب"""
        if not self.selected_account:
            messagebox.showwarning("تحذير", "يرجى اختيار حساب أولاً")
            return
        messagebox.showinfo("قريباً", "سيتم تطوير كشف الحساب قريباً")
        
    def accounts_report(self):
        """تقرير الحسابات"""
        messagebox.showinfo("قريباً", "سيتم تطوير تقرير الحسابات قريباً")
        
    def close_window(self):
        """إغلاق النافذة"""
        self.window.destroy()
