#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة قارئ الباركود
Barcode Scanner Window
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from datetime import datetime
import sys
import os
import random
import string

# إضافة مسار المجلدات للاستيراد
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'database'))

from database.models import Product

class BarcodeScannerWindow:
    """فئة نافذة قارئ الباركود"""
    
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.window = tk.Toplevel(parent)
        self.products = []
        self.scanned_products = []
        
        self.setup_window()
        self.setup_ui()
        self.load_products()
        self.setup_keyboard_shortcuts()
        
    def setup_window(self):
        """إعداد النافذة"""
        self.window.title("قارئ الباركود المتطور")
        self.window.geometry("900x700")
        self.window.resizable(True, True)
        
        # جعل النافذة في المقدمة
        self.window.transient(self.parent)
        self.window.grab_set()
        
        # توسيط النافذة
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (900 // 2)
        y = (self.window.winfo_screenheight() // 2) - (700 // 2)
        self.window.geometry(f"900x700+{x}+{y}")
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.window, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # إنشاء دفتر التبويبات
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # تبويب مسح الباركود
        self.create_scanner_tab(notebook)
        
        # تبويب إنشاء الباركود
        self.create_generator_tab(notebook)
        
        # تبويب إدارة الباركود
        self.create_management_tab(notebook)
        
        # أزرار التحكم
        self.create_control_buttons(main_frame)
        
    def create_scanner_tab(self, notebook):
        """إنشاء تبويب مسح الباركود"""
        scanner_frame = ttk.Frame(notebook, padding="10")
        notebook.add(scanner_frame, text="مسح الباركود")
        
        # منطقة إدخال الباركود
        input_frame = ttk.LabelFrame(scanner_frame, text="إدخال الباركود", padding="10")
        input_frame.pack(fill=tk.X, pady=(0, 10))
        
        # حقل إدخال الباركود
        ttk.Label(input_frame, text="الباركود:", font=('Arial', 12, 'bold')).pack(anchor=tk.W)
        self.barcode_var = tk.StringVar()
        barcode_entry = ttk.Entry(input_frame, textvariable=self.barcode_var, font=('Arial', 14), width=30)
        barcode_entry.pack(fill=tk.X, pady=(5, 10))
        barcode_entry.bind('<Return>', self.scan_barcode)
        barcode_entry.focus()
        
        # أزرار المسح
        buttons_frame = ttk.Frame(input_frame)
        buttons_frame.pack(fill=tk.X)
        
        ttk.Button(buttons_frame, text="مسح", command=self.scan_barcode).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="مسح من الكاميرا", command=self.scan_from_camera).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="مسح من صورة", command=self.scan_from_image).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="مسح", command=self.clear_barcode).pack(side=tk.RIGHT)
        
        # منطقة عرض النتائج
        results_frame = ttk.LabelFrame(scanner_frame, text="نتائج المسح", padding="10")
        results_frame.pack(fill=tk.BOTH, expand=True)
        
        # معلومات المنتج
        product_info_frame = ttk.Frame(results_frame)
        product_info_frame.pack(fill=tk.X, pady=(0, 10))
        
        # اسم المنتج
        ttk.Label(product_info_frame, text="اسم المنتج:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.product_name_var = tk.StringVar()
        ttk.Label(product_info_frame, textvariable=self.product_name_var, font=('Arial', 12, 'bold'), 
                 foreground='blue').grid(row=0, column=1, sticky=tk.W)
        
        # السعر
        ttk.Label(product_info_frame, text="سعر البيع:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10))
        self.product_price_var = tk.StringVar()
        ttk.Label(product_info_frame, textvariable=self.product_price_var, font=('Arial', 12, 'bold'), 
                 foreground='green').grid(row=1, column=1, sticky=tk.W)
        
        # الكمية المتاحة
        ttk.Label(product_info_frame, text="الكمية المتاحة:").grid(row=2, column=0, sticky=tk.W, padx=(0, 10))
        self.product_quantity_var = tk.StringVar()
        ttk.Label(product_info_frame, textvariable=self.product_quantity_var, font=('Arial', 12, 'bold'), 
                 foreground='red').grid(row=2, column=1, sticky=tk.W)
        
        # جدول المنتجات الممسوحة
        scanned_frame = ttk.LabelFrame(results_frame, text="المنتجات الممسوحة", padding="5")
        scanned_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))
        
        columns = ('الباركود', 'اسم المنتج', 'السعر', 'الكمية', 'الوقت')
        self.scanned_tree = ttk.Treeview(scanned_frame, columns=columns, show='headings', height=8)
        
        # تعريف الأعمدة
        for col in columns:
            self.scanned_tree.heading(col, text=col)
            
        self.scanned_tree.column('الباركود', width=120, anchor=tk.CENTER)
        self.scanned_tree.column('اسم المنتج', width=200, anchor=tk.W)
        self.scanned_tree.column('السعر', width=80, anchor=tk.E)
        self.scanned_tree.column('الكمية', width=80, anchor=tk.E)
        self.scanned_tree.column('الوقت', width=120, anchor=tk.CENTER)
        
        # شريط التمرير
        scrollbar_scanned = ttk.Scrollbar(scanned_frame, orient=tk.VERTICAL, command=self.scanned_tree.yview)
        self.scanned_tree.configure(yscrollcommand=scrollbar_scanned.set)
        
        # تخطيط الجدول
        self.scanned_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_scanned.pack(side=tk.RIGHT, fill=tk.Y)
        
    def create_generator_tab(self, notebook):
        """إنشاء تبويب إنشاء الباركود"""
        generator_frame = ttk.Frame(notebook, padding="10")
        notebook.add(generator_frame, text="إنشاء الباركود")
        
        # منطقة إنشاء الباركود
        create_frame = ttk.LabelFrame(generator_frame, text="إنشاء باركود جديد", padding="10")
        create_frame.pack(fill=tk.X, pady=(0, 10))
        
        # اختيار المنتج
        ttk.Label(create_frame, text="اختر المنتج:").grid(row=0, column=0, sticky=tk.W, pady=(0, 10))
        self.product_combo_var = tk.StringVar()
        self.product_combo = ttk.Combobox(create_frame, textvariable=self.product_combo_var, width=40)
        self.product_combo.grid(row=0, column=1, sticky=tk.W+tk.E, pady=(0, 10))
        
        # الباركود المقترح
        ttk.Label(create_frame, text="الباركود المقترح:").grid(row=1, column=0, sticky=tk.W, pady=(0, 10))
        self.suggested_barcode_var = tk.StringVar()
        barcode_frame = ttk.Frame(create_frame)
        barcode_frame.grid(row=1, column=1, sticky=tk.W+tk.E, pady=(0, 10))
        
        ttk.Entry(barcode_frame, textvariable=self.suggested_barcode_var, width=30).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(barcode_frame, text="إنشاء تلقائي", command=self.generate_barcode).pack(side=tk.RIGHT, padx=(5, 0))
        
        # نوع الباركود
        ttk.Label(create_frame, text="نوع الباركود:").grid(row=2, column=0, sticky=tk.W, pady=(0, 10))
        self.barcode_type_var = tk.StringVar(value="CODE128")
        type_combo = ttk.Combobox(create_frame, textvariable=self.barcode_type_var, width=37)
        type_combo['values'] = ('CODE128', 'CODE39', 'EAN13', 'EAN8', 'UPC')
        type_combo.grid(row=2, column=1, sticky=tk.W+tk.E, pady=(0, 10))
        
        # تكوين الأعمدة
        create_frame.columnconfigure(1, weight=1)
        
        # أزرار الإنشاء
        buttons_frame = ttk.Frame(create_frame)
        buttons_frame.grid(row=3, column=0, columnspan=2, pady=(10, 0))
        
        ttk.Button(buttons_frame, text="إنشاء الباركود", command=self.create_barcode).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="حفظ للمنتج", command=self.save_barcode_to_product).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="طباعة", command=self.print_barcode).pack(side=tk.LEFT)
        
        # منطقة عرض الباركود
        display_frame = ttk.LabelFrame(generator_frame, text="عرض الباركود", padding="10")
        display_frame.pack(fill=tk.BOTH, expand=True)
        
        # منطقة عرض الباركود (placeholder)
        self.barcode_display = tk.Label(display_frame, text="سيتم عرض الباركود هنا", 
                                       bg='white', relief=tk.SUNKEN, height=8)
        self.barcode_display.pack(fill=tk.BOTH, expand=True, pady=10)
        
    def create_management_tab(self, notebook):
        """إنشاء تبويب إدارة الباركود"""
        management_frame = ttk.Frame(notebook, padding="10")
        notebook.add(management_frame, text="إدارة الباركود")
        
        # شريط الأدوات
        toolbar = ttk.Frame(management_frame)
        toolbar.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(toolbar, text="تحديث القائمة", command=self.load_products).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(toolbar, text="تصدير قائمة", command=self.export_barcodes).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(toolbar, text="استيراد باركود", command=self.import_barcodes).pack(side=tk.LEFT)
        
        # جدول المنتجات والباركود
        products_frame = ttk.LabelFrame(management_frame, text="المنتجات والباركود", padding="5")
        products_frame.pack(fill=tk.BOTH, expand=True)
        
        columns = ('اسم المنتج', 'الباركود', 'الوحدة', 'السعر', 'الكمية', 'الحالة')
        self.products_tree = ttk.Treeview(products_frame, columns=columns, show='headings', height=15)
        
        # تعريف الأعمدة
        for col in columns:
            self.products_tree.heading(col, text=col)
            
        self.products_tree.column('اسم المنتج', width=200, anchor=tk.W)
        self.products_tree.column('الباركود', width=120, anchor=tk.CENTER)
        self.products_tree.column('الوحدة', width=80, anchor=tk.CENTER)
        self.products_tree.column('السعر', width=80, anchor=tk.E)
        self.products_tree.column('الكمية', width=80, anchor=tk.E)
        self.products_tree.column('الحالة', width=100, anchor=tk.CENTER)
        
        # شريط التمرير
        scrollbar_products = ttk.Scrollbar(products_frame, orient=tk.VERTICAL, command=self.products_tree.yview)
        self.products_tree.configure(yscrollcommand=scrollbar_products.set)
        
        # تخطيط الجدول
        self.products_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_products.pack(side=tk.RIGHT, fill=tk.Y)
        
        # ربط الأحداث
        self.products_tree.bind('<Double-1>', self.edit_product_barcode)
        
    def create_control_buttons(self, parent):
        """إنشاء أزرار التحكم"""
        buttons_frame = ttk.Frame(parent)
        buttons_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(buttons_frame, text="إغلاق", command=self.close_window).pack(side=tk.RIGHT)
        ttk.Button(buttons_frame, text="مسح الكل", command=self.clear_all).pack(side=tk.RIGHT, padx=(0, 10))
        ttk.Button(buttons_frame, text="تصدير النتائج", command=self.export_results).pack(side=tk.RIGHT, padx=(0, 10))
        
    def setup_keyboard_shortcuts(self):
        """إعداد اختصارات لوحة المفاتيح"""
        self.window.bind('<F1>', lambda e: self.scan_barcode())
        self.window.bind('<F2>', lambda e: self.generate_barcode())
        self.window.bind('<F3>', lambda e: self.create_barcode())
        self.window.bind('<F5>', lambda e: self.load_products())
        self.window.bind('<Escape>', lambda e: self.close_window())
        
    def load_products(self):
        """تحميل المنتجات من قاعدة البيانات"""
        try:
            self.products = Product.get_all(self.db_manager)
            
            # تحديث قائمة المنتجات في تبويب الإنشاء
            product_names = [f"{p.name} - {p.id}" for p in self.products]
            self.product_combo['values'] = product_names
            
            # تحديث جدول إدارة الباركود
            for item in self.products_tree.get_children():
                self.products_tree.delete(item)
                
            for product in self.products:
                status = "يوجد باركود" if product.barcode else "لا يوجد باركود"
                tags = () if product.barcode else ('no_barcode',)
                
                self.products_tree.insert('', tk.END, values=(
                    product.name,
                    product.barcode or "غير محدد",
                    product.unit,
                    f"{product.sale_price:.2f}",
                    f"{product.quantity:.2f}",
                    status
                ), tags=tags)
                
            # تكوين ألوان المنتجات بدون باركود
            self.products_tree.tag_configure('no_barcode', background='#ffeeee')
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل المنتجات: {str(e)}")
            
    def scan_barcode(self, event=None):
        """مسح الباركود"""
        barcode = self.barcode_var.get().strip()
        if not barcode:
            messagebox.showwarning("تحذير", "يرجى إدخال الباركود")
            return
            
        try:
            # البحث عن المنتج بالباركود
            product = Product.get_by_barcode(self.db_manager, barcode)
            
            if product:
                # عرض معلومات المنتج
                self.product_name_var.set(product.name)
                self.product_price_var.set(f"{product.sale_price:.2f} ج.م")
                self.product_quantity_var.set(f"{product.quantity:.2f} {product.unit}")
                
                # إضافة للقائمة الممسوحة
                scan_time = datetime.now().strftime("%H:%M:%S")
                self.scanned_tree.insert('', 0, values=(
                    barcode,
                    product.name,
                    f"{product.sale_price:.2f}",
                    f"{product.quantity:.2f}",
                    scan_time
                ))
                
                # إضافة للقائمة المحلية
                self.scanned_products.append({
                    'barcode': barcode,
                    'product': product,
                    'scan_time': scan_time
                })
                
                # مسح حقل الباركود للمسح التالي
                self.barcode_var.set('')
                
                # صوت نجاح (اختياري)
                self.window.bell()
                
            else:
                # المنتج غير موجود
                self.product_name_var.set("منتج غير موجود")
                self.product_price_var.set("---")
                self.product_quantity_var.set("---")
                
                messagebox.showwarning("تحذير", f"لم يتم العثور على منتج بالباركود: {barcode}")
                
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في مسح الباركود: {str(e)}")
            
    def scan_from_camera(self):
        """مسح الباركود من الكاميرا"""
        try:
            # محاولة استيراد مكتبة OpenCV
            import cv2
            from pyzbar import pyzbar
            
            # فتح الكاميرا
            cap = cv2.VideoCapture(0)
            
            if not cap.isOpened():
                messagebox.showerror("خطأ", "لا يمكن فتح الكاميرا")
                return
                
            messagebox.showinfo("معلومات", "اضغط 'q' لإغلاق الكاميرا أو 'Space' لالتقاط صورة")
            
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                    
                # البحث عن الباركود في الإطار
                barcodes = pyzbar.decode(frame)
                
                for barcode in barcodes:
                    # استخراج البيانات
                    barcode_data = barcode.data.decode('utf-8')
                    barcode_type = barcode.type
                    
                    # رسم مربع حول الباركود
                    (x, y, w, h) = barcode.rect
                    cv2.rectangle(frame, (x, y), (x + w, y + h), (0, 255, 0), 2)
                    
                    # عرض البيانات
                    text = f"{barcode_data} ({barcode_type})"
                    cv2.putText(frame, text, (x, y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
                    
                    # تعيين الباركود في الحقل
                    self.barcode_var.set(barcode_data)
                    
                # عرض الإطار
                cv2.imshow('Barcode Scanner', frame)
                
                # التحكم في الكاميرا
                key = cv2.waitKey(1) & 0xFF
                if key == ord('q'):
                    break
                elif key == ord(' '):  # مسافة لالتقاط صورة
                    if barcodes:
                        break
                        
            # إغلاق الكاميرا
            cap.release()
            cv2.destroyAllWindows()
            
            # مسح الباركود إذا تم العثور عليه
            if self.barcode_var.get():
                self.scan_barcode()
                
        except ImportError:
            messagebox.showerror("خطأ", "مكتبات الكاميرا غير مثبتة\nيرجى تثبيت opencv-python و pyzbar")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في مسح الكاميرا: {str(e)}")
            
    def scan_from_image(self):
        """مسح الباركود من صورة"""
        try:
            # اختيار ملف الصورة
            file_path = filedialog.askopenfilename(
                title="اختر صورة الباركود",
                filetypes=[("Image files", "*.png *.jpg *.jpeg *.gif *.bmp"), ("All files", "*.*")]
            )
            
            if not file_path:
                return
                
            # محاولة استيراد المكتبات المطلوبة
            import cv2
            from pyzbar import pyzbar
            
            # قراءة الصورة
            image = cv2.imread(file_path)
            
            if image is None:
                messagebox.showerror("خطأ", "لا يمكن قراءة الصورة")
                return
                
            # البحث عن الباركود
            barcodes = pyzbar.decode(image)
            
            if barcodes:
                # استخراج أول باركود
                barcode = barcodes[0]
                barcode_data = barcode.data.decode('utf-8')
                
                # تعيين الباركود في الحقل
                self.barcode_var.set(barcode_data)
                
                # مسح الباركود
                self.scan_barcode()
                
                messagebox.showinfo("نجح", f"تم العثور على الباركود: {barcode_data}")
            else:
                messagebox.showwarning("تحذير", "لم يتم العثور على باركود في الصورة")
                
        except ImportError:
            messagebox.showerror("خطأ", "مكتبات معالجة الصور غير مثبتة\nيرجى تثبيت opencv-python و pyzbar")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في مسح الصورة: {str(e)}")
            
    def clear_barcode(self):
        """مسح حقل الباركود"""
        self.barcode_var.set('')
        self.product_name_var.set('')
        self.product_price_var.set('')
        self.product_quantity_var.set('')
        
    def generate_barcode(self):
        """إنشاء باركود تلقائي"""
        # إنشاء باركود عشوائي
        barcode = ''.join(random.choices(string.digits, k=12))
        self.suggested_barcode_var.set(barcode)
        
    def create_barcode(self):
        """إنشاء صورة الباركود"""
        barcode = self.suggested_barcode_var.get().strip()
        if not barcode:
            messagebox.showwarning("تحذير", "يرجى إدخال الباركود")
            return
            
        try:
            # محاولة إنشاء الباركود باستخدام python-barcode
            from barcode import Code128
            from barcode.writer import ImageWriter
            
            # إنشاء الباركود
            code = Code128(barcode, writer=ImageWriter())
            
            # حفظ مؤقت
            temp_path = f"temp_barcode_{barcode}"
            code.save(temp_path)
            
            # عرض رسالة نجاح
            self.barcode_display.config(text=f"تم إنشاء الباركود: {barcode}\nمحفوظ في: {temp_path}.png")
            
            messagebox.showinfo("نجح", f"تم إنشاء الباركود بنجاح: {barcode}")
            
        except ImportError:
            # إنشاء باركود نصي بسيط
            self.barcode_display.config(text=f"الباركود: {barcode}\n(يتطلب مكتبة python-barcode للعرض المرئي)")
            messagebox.showinfo("معلومات", "تم إنشاء الباركود\nلعرض مرئي أفضل، يرجى تثبيت مكتبة python-barcode")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إنشاء الباركود: {str(e)}")
            
    def save_barcode_to_product(self):
        """حفظ الباركود للمنتج"""
        product_selection = self.product_combo_var.get()
        barcode = self.suggested_barcode_var.get().strip()
        
        if not product_selection or not barcode:
            messagebox.showwarning("تحذير", "يرجى اختيار منتج وإدخال الباركود")
            return
            
        try:
            # استخراج معرف المنتج
            product_id = int(product_selection.split(' - ')[-1])
            
            # الحصول على المنتج
            product = Product.get_by_id(self.db_manager, product_id)
            if not product:
                messagebox.showerror("خطأ", "المنتج غير موجود")
                return
                
            # تحديث الباركود
            product.barcode = barcode
            product.save()
            
            messagebox.showinfo("نجح", f"تم حفظ الباركود للمنتج: {product.name}")
            
            # تحديث القوائم
            self.load_products()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في حفظ الباركود: {str(e)}")
            
    def print_barcode(self):
        """طباعة الباركود"""
        messagebox.showinfo("قريباً", "سيتم تطوير طباعة الباركود قريباً")
        
    def edit_product_barcode(self, event):
        """تعديل باركود المنتج"""
        selection = self.products_tree.selection()
        if not selection:
            return
            
        item = self.products_tree.item(selection[0])
        product_name = item['values'][0]
        current_barcode = item['values'][1]
        
        # نافذة تعديل الباركود
        edit_window = tk.Toplevel(self.window)
        edit_window.title("تعديل الباركود")
        edit_window.geometry("400x200")
        edit_window.transient(self.window)
        edit_window.grab_set()
        
        # محتوى النافذة
        ttk.Label(edit_window, text=f"المنتج: {product_name}").pack(pady=10)
        ttk.Label(edit_window, text="الباركود الجديد:").pack()
        
        new_barcode_var = tk.StringVar(value=current_barcode if current_barcode != "غير محدد" else "")
        ttk.Entry(edit_window, textvariable=new_barcode_var, width=30).pack(pady=10)
        
        def save_new_barcode():
            new_barcode = new_barcode_var.get().strip()
            if new_barcode:
                # البحث عن المنتج وتحديث الباركود
                for product in self.products:
                    if product.name == product_name:
                        product.barcode = new_barcode
                        product.save()
                        messagebox.showinfo("نجح", "تم تحديث الباركود بنجاح")
                        self.load_products()
                        edit_window.destroy()
                        break
            else:
                messagebox.showwarning("تحذير", "يرجى إدخال الباركود")
                
        ttk.Button(edit_window, text="حفظ", command=save_new_barcode).pack(pady=10)
        
    def export_barcodes(self):
        """تصدير قائمة الباركود"""
        messagebox.showinfo("قريباً", "سيتم تطوير تصدير قائمة الباركود قريباً")
        
    def import_barcodes(self):
        """استيراد باركود"""
        messagebox.showinfo("قريباً", "سيتم تطوير استيراد الباركود قريباً")
        
    def clear_all(self):
        """مسح جميع النتائج"""
        if messagebox.askyesno("تأكيد", "هل تريد مسح جميع النتائج؟"):
            # مسح قائمة المنتجات الممسوحة
            for item in self.scanned_tree.get_children():
                self.scanned_tree.delete(item)
            self.scanned_products.clear()
            
            # مسح معلومات المنتج
            self.clear_barcode()
            
    def export_results(self):
        """تصدير النتائج"""
        if not self.scanned_products:
            messagebox.showwarning("تحذير", "لا توجد نتائج للتصدير")
            return
        messagebox.showinfo("قريباً", "سيتم تطوير تصدير النتائج قريباً")
        
    def close_window(self):
        """إغلاق النافذة"""
        self.window.destroy()
