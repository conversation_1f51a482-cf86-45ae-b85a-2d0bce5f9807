#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة عرض الفواتير
Invoices Viewer Window
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, timedelta
import sys
import os

# إضافة مسار المجلدات للاستيراد
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'database'))

from database.models import SalesInvoice

class InvoicesViewerWindow:
    """فئة نافذة عرض الفواتير"""
    
    def __init__(self, parent, db_manager, invoice_type='sales'):
        self.parent = parent
        self.db_manager = db_manager
        self.invoice_type = invoice_type  # 'sales' أو 'purchase'
        self.window = tk.Toplevel(parent)
        self.invoices = []
        self.selected_invoice = None
        
        self.setup_window()
        self.setup_ui()
        self.load_invoices()
        self.setup_keyboard_shortcuts()
        
    def setup_window(self):
        """إعداد النافذة"""
        title = "عرض فواتير المبيعات" if self.invoice_type == 'sales' else "عرض فواتير المشتريات"
        self.window.title(title)
        self.window.geometry("1200x700")
        self.window.resizable(True, True)
        
        # جعل النافذة في المقدمة
        self.window.transient(self.parent)
        self.window.grab_set()
        
        # توسيط النافذة
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (1200 // 2)
        y = (self.window.winfo_screenheight() // 2) - (700 // 2)
        self.window.geometry(f"1200x700+{x}+{y}")
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.window, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # شريط البحث والفلاتر
        self.create_filters_section(main_frame)
        
        # منطقة المحتوى الرئيسي
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))
        
        # قائمة الفواتير (الجانب الأيسر)
        self.create_invoices_list(content_frame)
        
        # تفاصيل الفاتورة (الجانب الأيمن)
        self.create_invoice_details(content_frame)
        
        # أزرار التحكم
        self.create_control_buttons(main_frame)
        
    def create_filters_section(self, parent):
        """إنشاء قسم الفلاتر والبحث"""
        filters_frame = ttk.LabelFrame(parent, text="البحث والفلاتر", padding="10")
        filters_frame.pack(fill=tk.X, pady=(0, 10))
        
        # الصف الأول
        row1 = ttk.Frame(filters_frame)
        row1.pack(fill=tk.X, pady=(0, 5))
        
        # البحث برقم الفاتورة
        ttk.Label(row1, text="رقم الفاتورة:").pack(side=tk.LEFT)
        self.invoice_number_search_var = tk.StringVar()
        ttk.Entry(row1, textvariable=self.invoice_number_search_var, width=15).pack(side=tk.LEFT, padx=(5, 20))
        
        # البحث بالعميل/المورد
        label_text = "العميل:" if self.invoice_type == 'sales' else "المورد:"
        ttk.Label(row1, text=label_text).pack(side=tk.LEFT)
        self.customer_search_var = tk.StringVar()
        ttk.Entry(row1, textvariable=self.customer_search_var, width=20).pack(side=tk.LEFT, padx=(5, 20))
        
        # الصف الثاني
        row2 = ttk.Frame(filters_frame)
        row2.pack(fill=tk.X, pady=(0, 5))
        
        # فترة التاريخ
        ttk.Label(row2, text="من تاريخ:").pack(side=tk.LEFT)
        self.from_date_var = tk.StringVar(value=(datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d"))
        ttk.Entry(row2, textvariable=self.from_date_var, width=12).pack(side=tk.LEFT, padx=(5, 20))
        
        ttk.Label(row2, text="إلى تاريخ:").pack(side=tk.LEFT)
        self.to_date_var = tk.StringVar(value=datetime.now().strftime("%Y-%m-%d"))
        ttk.Entry(row2, textvariable=self.to_date_var, width=12).pack(side=tk.LEFT, padx=(5, 20))
        
        # حالة الفاتورة
        ttk.Label(row2, text="الحالة:").pack(side=tk.LEFT)
        self.status_var = tk.StringVar(value="الكل")
        status_combo = ttk.Combobox(row2, textvariable=self.status_var, width=12)
        status_combo['values'] = ('الكل', 'مفتوحة', 'مدفوعة', 'ملغية')
        status_combo.pack(side=tk.LEFT, padx=(5, 20))
        
        # أزرار البحث
        ttk.Button(row2, text="بحث", command=self.search_invoices).pack(side=tk.LEFT, padx=(10, 5))
        ttk.Button(row2, text="مسح", command=self.clear_filters).pack(side=tk.LEFT)
        
    def create_invoices_list(self, parent):
        """إنشاء قائمة الفواتير"""
        # إطار قائمة الفواتير
        list_frame = ttk.LabelFrame(parent, text="قائمة الفواتير", padding="5")
        list_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        # جدول الفواتير
        columns = ('رقم الفاتورة', 'التاريخ', 'العميل/المورد', 'الإجمالي', 'المدفوع', 'المتبقي', 'الحالة')
        self.invoices_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=20)
        
        # تعريف الأعمدة
        self.invoices_tree.heading('رقم الفاتورة', text='رقم الفاتورة')
        self.invoices_tree.heading('التاريخ', text='التاريخ')
        self.invoices_tree.heading('العميل/المورد', text='العميل' if self.invoice_type == 'sales' else 'المورد')
        self.invoices_tree.heading('الإجمالي', text='الإجمالي')
        self.invoices_tree.heading('المدفوع', text='المدفوع')
        self.invoices_tree.heading('المتبقي', text='المتبقي')
        self.invoices_tree.heading('الحالة', text='الحالة')
        
        self.invoices_tree.column('رقم الفاتورة', width=100, anchor=tk.CENTER)
        self.invoices_tree.column('التاريخ', width=100, anchor=tk.CENTER)
        self.invoices_tree.column('العميل/المورد', width=150, anchor=tk.W)
        self.invoices_tree.column('الإجمالي', width=100, anchor=tk.E)
        self.invoices_tree.column('المدفوع', width=100, anchor=tk.E)
        self.invoices_tree.column('المتبقي', width=100, anchor=tk.E)
        self.invoices_tree.column('الحالة', width=80, anchor=tk.CENTER)
        
        # شريط التمرير
        scrollbar_list = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.invoices_tree.yview)
        self.invoices_tree.configure(yscrollcommand=scrollbar_list.set)
        
        # تخطيط الجدول وشريط التمرير
        self.invoices_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_list.pack(side=tk.RIGHT, fill=tk.Y)
        
        # ربط الأحداث
        self.invoices_tree.bind('<<TreeviewSelect>>', self.on_invoice_select)
        self.invoices_tree.bind('<Double-1>', self.view_invoice_details)
        
    def create_invoice_details(self, parent):
        """إنشاء منطقة تفاصيل الفاتورة"""
        # إطار التفاصيل
        details_frame = ttk.LabelFrame(parent, text="تفاصيل الفاتورة", padding="10")
        details_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # معلومات الفاتورة الأساسية
        info_frame = ttk.Frame(details_frame)
        info_frame.pack(fill=tk.X, pady=(0, 10))
        
        # الصف الأول
        row1 = ttk.Frame(info_frame)
        row1.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(row1, text="رقم الفاتورة:", font=('Arial', 10, 'bold')).pack(side=tk.LEFT)
        self.detail_invoice_number_var = tk.StringVar()
        ttk.Label(row1, textvariable=self.detail_invoice_number_var, foreground='blue').pack(side=tk.LEFT, padx=(5, 20))
        
        ttk.Label(row1, text="التاريخ:", font=('Arial', 10, 'bold')).pack(side=tk.LEFT)
        self.detail_date_var = tk.StringVar()
        ttk.Label(row1, textvariable=self.detail_date_var).pack(side=tk.LEFT, padx=(5, 0))
        
        # الصف الثاني
        row2 = ttk.Frame(info_frame)
        row2.pack(fill=tk.X, pady=(0, 5))
        
        customer_label = "العميل:" if self.invoice_type == 'sales' else "المورد:"
        ttk.Label(row2, text=customer_label, font=('Arial', 10, 'bold')).pack(side=tk.LEFT)
        self.detail_customer_var = tk.StringVar()
        ttk.Label(row2, textvariable=self.detail_customer_var, foreground='green').pack(side=tk.LEFT, padx=(5, 20))
        
        ttk.Label(row2, text="الحالة:", font=('Arial', 10, 'bold')).pack(side=tk.LEFT)
        self.detail_status_var = tk.StringVar()
        ttk.Label(row2, textvariable=self.detail_status_var, foreground='red').pack(side=tk.LEFT, padx=(5, 0))
        
        # أصناف الفاتورة
        items_frame = ttk.LabelFrame(details_frame, text="أصناف الفاتورة", padding="5")
        items_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # جدول الأصناف
        items_columns = ('المنتج', 'الوحدة', 'الكمية', 'السعر', 'الإجمالي')
        self.items_tree = ttk.Treeview(items_frame, columns=items_columns, show='headings', height=8)
        
        # تعريف أعمدة الأصناف
        for col in items_columns:
            self.items_tree.heading(col, text=col)
            self.items_tree.column(col, width=100, anchor=tk.CENTER)
            
        # شريط التمرير للأصناف
        scrollbar_items = ttk.Scrollbar(items_frame, orient=tk.VERTICAL, command=self.items_tree.yview)
        self.items_tree.configure(yscrollcommand=scrollbar_items.set)
        
        # تخطيط جدول الأصناف
        self.items_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_items.pack(side=tk.RIGHT, fill=tk.Y)
        
        # إجماليات الفاتورة
        totals_frame = ttk.LabelFrame(details_frame, text="الإجماليات", padding="10")
        totals_frame.pack(fill=tk.X)
        
        # الصف الأول من الإجماليات
        totals_row1 = ttk.Frame(totals_frame)
        totals_row1.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(totals_row1, text="الإجمالي:").pack(side=tk.LEFT)
        self.detail_total_var = tk.StringVar()
        ttk.Label(totals_row1, textvariable=self.detail_total_var, font=('Arial', 12, 'bold')).pack(side=tk.LEFT, padx=(5, 20))
        
        ttk.Label(totals_row1, text="الخصم:").pack(side=tk.LEFT)
        self.detail_discount_var = tk.StringVar()
        ttk.Label(totals_row1, textvariable=self.detail_discount_var).pack(side=tk.LEFT, padx=(5, 20))
        
        # الصف الثاني من الإجماليات
        totals_row2 = ttk.Frame(totals_frame)
        totals_row2.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(totals_row2, text="الضريبة:").pack(side=tk.LEFT)
        self.detail_tax_var = tk.StringVar()
        ttk.Label(totals_row2, textvariable=self.detail_tax_var).pack(side=tk.LEFT, padx=(5, 20))
        
        ttk.Label(totals_row2, text="الإجمالي النهائي:").pack(side=tk.LEFT)
        self.detail_final_var = tk.StringVar()
        ttk.Label(totals_row2, textvariable=self.detail_final_var, font=('Arial', 12, 'bold'), 
                 foreground='red').pack(side=tk.LEFT, padx=(5, 20))
        
        # الصف الثالث من الإجماليات
        totals_row3 = ttk.Frame(totals_frame)
        totals_row3.pack(fill=tk.X)
        
        ttk.Label(totals_row3, text="المدفوع:").pack(side=tk.LEFT)
        self.detail_paid_var = tk.StringVar()
        ttk.Label(totals_row3, textvariable=self.detail_paid_var, foreground='green').pack(side=tk.LEFT, padx=(5, 20))
        
        ttk.Label(totals_row3, text="المتبقي:").pack(side=tk.LEFT)
        self.detail_remaining_var = tk.StringVar()
        ttk.Label(totals_row3, textvariable=self.detail_remaining_var, foreground='blue').pack(side=tk.LEFT, padx=(5, 0))
        
    def create_control_buttons(self, parent):
        """إنشاء أزرار التحكم"""
        buttons_frame = ttk.Frame(parent)
        buttons_frame.pack(fill=tk.X, pady=(10, 0))
        
        # أزرار الجانب الأيسر
        ttk.Button(buttons_frame, text="تحديث", command=self.load_invoices).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="طباعة الفاتورة", command=self.print_invoice).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="تعديل الفاتورة", command=self.edit_invoice).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="حذف الفاتورة", command=self.delete_invoice).pack(side=tk.LEFT, padx=(0, 10))
        
        # أزرار الجانب الأيمن
        ttk.Button(buttons_frame, text="إغلاق", command=self.close_window).pack(side=tk.RIGHT)
        ttk.Button(buttons_frame, text="تصدير", command=self.export_invoices).pack(side=tk.RIGHT, padx=(0, 10))
        
    def setup_keyboard_shortcuts(self):
        """إعداد اختصارات لوحة المفاتيح"""
        self.window.bind('<F5>', lambda e: self.load_invoices())
        self.window.bind('<F1>', lambda e: self.search_invoices())
        self.window.bind('<Delete>', lambda e: self.delete_invoice())
        self.window.bind('<Escape>', lambda e: self.close_window())
        
    def load_invoices(self):
        """تحميل الفواتير من قاعدة البيانات"""
        try:
            # مسح البيانات الحالية
            for item in self.invoices_tree.get_children():
                self.invoices_tree.delete(item)
                
            # تحديد الجدول والعمود حسب نوع الفاتورة
            if self.invoice_type == 'sales':
                table_name = 'sales_invoices'
                customer_table = 'customers'
                customer_column = 'customer_id'
            else:
                table_name = 'purchase_invoices'
                customer_table = 'suppliers'
                customer_column = 'supplier_id'
                
            # استعلام تحميل الفواتير
            query = f"""
                SELECT i.*, c.name as customer_name
                FROM {table_name} i
                LEFT JOIN {customer_table} c ON i.{customer_column} = c.id
                ORDER BY i.invoice_date DESC
                LIMIT 1000
            """
            
            results = self.db_manager.execute_query(query)
            
            self.invoices = []
            for row in results:
                invoice_data = dict(row)
                self.invoices.append(invoice_data)
                
                # تحديد لون الصف حسب الحالة
                tags = ()
                if invoice_data['status'] == 'ملغية':
                    tags = ('cancelled',)
                elif invoice_data['remaining_amount'] > 0:
                    tags = ('pending',)
                else:
                    tags = ('paid',)
                
                # إضافة للجدول
                self.invoices_tree.insert('', tk.END, values=(
                    invoice_data['invoice_number'],
                    invoice_data['invoice_date'][:10] if invoice_data['invoice_date'] else '',
                    invoice_data['customer_name'] or 'غير محدد',
                    f"{invoice_data['final_amount']:.2f}",
                    f"{invoice_data['paid_amount']:.2f}",
                    f"{invoice_data['remaining_amount']:.2f}",
                    invoice_data['status']
                ), tags=tags)
                
            # تكوين ألوان الصفوف
            self.invoices_tree.tag_configure('cancelled', background='#ffcccc')
            self.invoices_tree.tag_configure('pending', background='#ffffcc')
            self.invoices_tree.tag_configure('paid', background='#ccffcc')
                
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل الفواتير: {str(e)}")
            
    def search_invoices(self):
        """البحث في الفواتير"""
        try:
            # مسح البيانات الحالية
            for item in self.invoices_tree.get_children():
                self.invoices_tree.delete(item)
                
            # بناء شروط البحث
            conditions = []
            params = []
            
            # البحث برقم الفاتورة
            if self.invoice_number_search_var.get().strip():
                conditions.append("i.invoice_number LIKE ?")
                params.append(f"%{self.invoice_number_search_var.get().strip()}%")
                
            # البحث بالعميل/المورد
            if self.customer_search_var.get().strip():
                conditions.append("c.name LIKE ?")
                params.append(f"%{self.customer_search_var.get().strip()}%")
                
            # فلتر التاريخ
            conditions.append("DATE(i.invoice_date) BETWEEN ? AND ?")
            params.extend([self.from_date_var.get(), self.to_date_var.get()])
            
            # فلتر الحالة
            if self.status_var.get() != "الكل":
                conditions.append("i.status = ?")
                params.append(self.status_var.get())
                
            # تحديد الجدول والعمود حسب نوع الفاتورة
            if self.invoice_type == 'sales':
                table_name = 'sales_invoices'
                customer_table = 'customers'
                customer_column = 'customer_id'
            else:
                table_name = 'purchase_invoices'
                customer_table = 'suppliers'
                customer_column = 'supplier_id'
                
            # بناء الاستعلام
            where_clause = " AND ".join(conditions) if conditions else "1=1"
            query = f"""
                SELECT i.*, c.name as customer_name
                FROM {table_name} i
                LEFT JOIN {customer_table} c ON i.{customer_column} = c.id
                WHERE {where_clause}
                ORDER BY i.invoice_date DESC
                LIMIT 1000
            """
            
            results = self.db_manager.execute_query(query, params)
            
            self.invoices = []
            for row in results:
                invoice_data = dict(row)
                self.invoices.append(invoice_data)
                
                # تحديد لون الصف حسب الحالة
                tags = ()
                if invoice_data['status'] == 'ملغية':
                    tags = ('cancelled',)
                elif invoice_data['remaining_amount'] > 0:
                    tags = ('pending',)
                else:
                    tags = ('paid',)
                
                # إضافة للجدول
                self.invoices_tree.insert('', tk.END, values=(
                    invoice_data['invoice_number'],
                    invoice_data['invoice_date'][:10] if invoice_data['invoice_date'] else '',
                    invoice_data['customer_name'] or 'غير محدد',
                    f"{invoice_data['final_amount']:.2f}",
                    f"{invoice_data['paid_amount']:.2f}",
                    f"{invoice_data['remaining_amount']:.2f}",
                    invoice_data['status']
                ), tags=tags)
                
            # تكوين ألوان الصفوف
            self.invoices_tree.tag_configure('cancelled', background='#ffcccc')
            self.invoices_tree.tag_configure('pending', background='#ffffcc')
            self.invoices_tree.tag_configure('paid', background='#ccffcc')
                
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في البحث: {str(e)}")
            
    def clear_filters(self):
        """مسح الفلاتر"""
        self.invoice_number_search_var.set('')
        self.customer_search_var.set('')
        self.from_date_var.set((datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d"))
        self.to_date_var.set(datetime.now().strftime("%Y-%m-%d"))
        self.status_var.set('الكل')
        self.load_invoices()
        
    def on_invoice_select(self, event):
        """عند اختيار فاتورة من القائمة"""
        selection = self.invoices_tree.selection()
        if selection:
            item = self.invoices_tree.item(selection[0])
            invoice_number = item['values'][0]
            
            # البحث عن الفاتورة في القائمة
            for invoice in self.invoices:
                if invoice['invoice_number'] == invoice_number:
                    self.selected_invoice = invoice
                    self.load_invoice_details(invoice)
                    break
                    
    def load_invoice_details(self, invoice):
        """تحميل تفاصيل الفاتورة"""
        try:
            # تحميل المعلومات الأساسية
            self.detail_invoice_number_var.set(invoice['invoice_number'])
            self.detail_date_var.set(invoice['invoice_date'][:16] if invoice['invoice_date'] else '')
            self.detail_customer_var.set(invoice.get('customer_name', 'غير محدد'))
            self.detail_status_var.set(invoice['status'])
            
            # تحميل الإجماليات
            self.detail_total_var.set(f"{invoice['total_amount']:.2f} ج.م")
            self.detail_discount_var.set(f"{invoice['discount']:.2f} ج.م")
            self.detail_tax_var.set(f"{invoice['tax']:.2f} ج.م")
            self.detail_final_var.set(f"{invoice['final_amount']:.2f} ج.م")
            self.detail_paid_var.set(f"{invoice['paid_amount']:.2f} ج.م")
            self.detail_remaining_var.set(f"{invoice['remaining_amount']:.2f} ج.م")
            
            # تحميل أصناف الفاتورة
            self.load_invoice_items(invoice['id'])
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل تفاصيل الفاتورة: {str(e)}")
            
    def load_invoice_items(self, invoice_id):
        """تحميل أصناف الفاتورة"""
        try:
            # مسح الأصناف الحالية
            for item in self.items_tree.get_children():
                self.items_tree.delete(item)
                
            # تحديد الجدول حسب نوع الفاتورة
            items_table = 'sales_invoice_items' if self.invoice_type == 'sales' else 'purchase_invoice_items'
            
            # استعلام تحميل الأصناف
            query = f"""
                SELECT ii.*, p.name as product_name, p.unit
                FROM {items_table} ii
                JOIN products p ON ii.product_id = p.id
                WHERE ii.invoice_id = ?
                ORDER BY ii.id
            """
            
            results = self.db_manager.execute_query(query, (invoice_id,))
            
            for row in results:
                self.items_tree.insert('', tk.END, values=(
                    row['product_name'],
                    row['unit'],
                    f"{row['quantity']:.2f}",
                    f"{row['unit_price']:.2f}",
                    f"{row['total_price']:.2f}"
                ))
                
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل أصناف الفاتورة: {str(e)}")
            
    def view_invoice_details(self, event):
        """عرض تفاصيل الفاتورة (عند النقر المزدوج)"""
        self.on_invoice_select(event)
        
    def print_invoice(self):
        """طباعة الفاتورة"""
        if not self.selected_invoice:
            messagebox.showwarning("تحذير", "يرجى اختيار فاتورة للطباعة")
            return
        messagebox.showinfo("قريباً", "سيتم تطوير طباعة الفاتورة قريباً")
        
    def edit_invoice(self):
        """تعديل الفاتورة"""
        if not self.selected_invoice:
            messagebox.showwarning("تحذير", "يرجى اختيار فاتورة للتعديل")
            return
        messagebox.showinfo("قريباً", "سيتم تطوير تعديل الفاتورة قريباً")
        
    def delete_invoice(self):
        """حذف الفاتورة"""
        if not self.selected_invoice:
            messagebox.showwarning("تحذير", "يرجى اختيار فاتورة للحذف")
            return
            
        if messagebox.askyesno("تأكيد الحذف", 
                              f"هل تريد حذف الفاتورة '{self.selected_invoice['invoice_number']}'؟\nهذا الإجراء لا يمكن التراجع عنه"):
            try:
                # تحديد الجدول حسب نوع الفاتورة
                if self.invoice_type == 'sales':
                    invoice_table = 'sales_invoices'
                    items_table = 'sales_invoice_items'
                else:
                    invoice_table = 'purchase_invoices'
                    items_table = 'purchase_invoice_items'
                
                # حذف أصناف الفاتورة أولاً
                self.db_manager.execute_query(f"DELETE FROM {items_table} WHERE invoice_id=?", 
                                            (self.selected_invoice['id'],))
                
                # حذف الفاتورة
                self.db_manager.execute_query(f"DELETE FROM {invoice_table} WHERE id=?", 
                                            (self.selected_invoice['id'],))
                
                messagebox.showinfo("نجح", "تم حذف الفاتورة بنجاح")
                
                # تحديث القائمة
                self.load_invoices()
                
                # مسح التفاصيل
                self.clear_details()
                
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في حذف الفاتورة: {str(e)}")
                
    def clear_details(self):
        """مسح تفاصيل الفاتورة"""
        self.detail_invoice_number_var.set('')
        self.detail_date_var.set('')
        self.detail_customer_var.set('')
        self.detail_status_var.set('')
        self.detail_total_var.set('')
        self.detail_discount_var.set('')
        self.detail_tax_var.set('')
        self.detail_final_var.set('')
        self.detail_paid_var.set('')
        self.detail_remaining_var.set('')
        
        # مسح أصناف الفاتورة
        for item in self.items_tree.get_children():
            self.items_tree.delete(item)
            
    def export_invoices(self):
        """تصدير الفواتير"""
        messagebox.showinfo("قريباً", "سيتم تطوير تصدير الفواتير قريباً")
        
    def close_window(self):
        """إغلاق النافذة"""
        self.window.destroy()
