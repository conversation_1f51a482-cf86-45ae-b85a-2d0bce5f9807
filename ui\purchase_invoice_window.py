#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة فاتورة المشتريات
Purchase Invoice Window
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
import sys
import os

# إضافة مسار المجلدات للاستيراد
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'database'))

from database.models import Supplier, Product

class PurchaseInvoiceWindow:
    """فئة نافذة فاتورة المشتريات"""
    
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.window = tk.Toplevel(parent)
        self.invoice_items = []
        self.suppliers = []
        self.products = []
        
        self.setup_window()
        self.load_data()
        self.setup_ui()
        self.setup_keyboard_shortcuts()
        
    def setup_window(self):
        """إعداد النافذة"""
        self.window.title("فاتورة مشتريات جديدة")
        self.window.geometry("1000x700")
        self.window.resizable(True, True)
        
        # جعل النافذة في المقدمة
        self.window.transient(self.parent)
        self.window.grab_set()
        
        # توسيط النافذة
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (1000 // 2)
        y = (self.window.winfo_screenheight() // 2) - (700 // 2)
        self.window.geometry(f"1000x700+{x}+{y}")
        
    def load_data(self):
        """تحميل البيانات من قاعدة البيانات"""
        try:
            self.suppliers = Supplier.get_all(self.db_manager)
            self.products = Product.get_all(self.db_manager)
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل البيانات: {str(e)}")
            
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.window, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # معلومات الفاتورة
        self.create_invoice_info_section(main_frame)
        
        # معلومات المورد
        self.create_supplier_section(main_frame)
        
        # أصناف الفاتورة
        self.create_items_section(main_frame)
        
        # إجماليات الفاتورة
        self.create_totals_section(main_frame)
        
        # أزرار التحكم
        self.create_control_buttons(main_frame)
        
    def create_invoice_info_section(self, parent):
        """إنشاء قسم معلومات الفاتورة"""
        info_frame = ttk.LabelFrame(parent, text="معلومات الفاتورة", padding="10")
        info_frame.pack(fill=tk.X, pady=(0, 10))
        
        # الصف الأول
        row1 = ttk.Frame(info_frame)
        row1.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(row1, text="رقم الفاتورة:").pack(side=tk.LEFT)
        self.invoice_number_var = tk.StringVar()
        self.invoice_number_var.set(self.db_manager.get_next_invoice_number('purchase'))
        ttk.Entry(row1, textvariable=self.invoice_number_var, state='readonly', width=15).pack(side=tk.LEFT, padx=(5, 20))
        
        ttk.Label(row1, text="التاريخ:").pack(side=tk.LEFT)
        self.invoice_date_var = tk.StringVar()
        self.invoice_date_var.set(datetime.now().strftime("%Y-%m-%d"))
        ttk.Entry(row1, textvariable=self.invoice_date_var, width=15).pack(side=tk.LEFT, padx=(5, 20))
        
        ttk.Label(row1, text="الوقت:").pack(side=tk.LEFT)
        self.invoice_time_var = tk.StringVar()
        self.invoice_time_var.set(datetime.now().strftime("%H:%M:%S"))
        ttk.Entry(row1, textvariable=self.invoice_time_var, state='readonly', width=15).pack(side=tk.LEFT, padx=(5, 0))
        
    def create_supplier_section(self, parent):
        """إنشاء قسم معلومات المورد"""
        supplier_frame = ttk.LabelFrame(parent, text="معلومات المورد", padding="10")
        supplier_frame.pack(fill=tk.X, pady=(0, 10))
        
        # الصف الأول
        row1 = ttk.Frame(supplier_frame)
        row1.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(row1, text="المورد:").pack(side=tk.LEFT)
        self.supplier_var = tk.StringVar()
        supplier_combo = ttk.Combobox(row1, textvariable=self.supplier_var, width=30)
        supplier_combo['values'] = [f"{s.name} - {s.phone}" for s in self.suppliers]
        supplier_combo.pack(side=tk.LEFT, padx=(5, 20))
        supplier_combo.bind('<<ComboboxSelected>>', self.on_supplier_selected)
        
        ttk.Button(row1, text="مورد جديد", command=self.add_new_supplier).pack(side=tk.LEFT, padx=(5, 20))
        
        # معلومات المورد المحدد
        self.supplier_info_var = tk.StringVar()
        ttk.Label(row1, textvariable=self.supplier_info_var, foreground="blue").pack(side=tk.LEFT)
        
    def create_items_section(self, parent):
        """إنشاء قسم أصناف الفاتورة"""
        items_frame = ttk.LabelFrame(parent, text="أصناف الفاتورة", padding="10")
        items_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # إطار إضافة صنف
        add_item_frame = ttk.Frame(items_frame)
        add_item_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(add_item_frame, text="المنتج:").pack(side=tk.LEFT)
        self.product_var = tk.StringVar()
        product_combo = ttk.Combobox(add_item_frame, textvariable=self.product_var, width=25)
        product_combo['values'] = [f"{p.name} - {p.barcode}" for p in self.products]
        product_combo.pack(side=tk.LEFT, padx=(5, 10))
        product_combo.bind('<<ComboboxSelected>>', self.on_product_selected)
        
        ttk.Label(add_item_frame, text="الكمية:").pack(side=tk.LEFT)
        self.quantity_var = tk.StringVar(value="1")
        quantity_entry = ttk.Entry(add_item_frame, textvariable=self.quantity_var, width=10)
        quantity_entry.pack(side=tk.LEFT, padx=(5, 10))
        
        ttk.Label(add_item_frame, text="سعر الشراء:").pack(side=tk.LEFT)
        self.unit_price_var = tk.StringVar()
        price_entry = ttk.Entry(add_item_frame, textvariable=self.unit_price_var, width=10)
        price_entry.pack(side=tk.LEFT, padx=(5, 10))
        
        ttk.Button(add_item_frame, text="إضافة", command=self.add_item).pack(side=tk.LEFT, padx=(10, 0))
        
        # جدول الأصناف
        columns = ('المنتج', 'الوحدة', 'الكمية', 'سعر الشراء', 'الإجمالي')
        self.items_tree = ttk.Treeview(items_frame, columns=columns, show='headings', height=10)
        
        # تعريف الأعمدة
        for col in columns:
            self.items_tree.heading(col, text=col)
            self.items_tree.column(col, width=120, anchor=tk.CENTER)
            
        # شريط التمرير
        scrollbar = ttk.Scrollbar(items_frame, orient=tk.VERTICAL, command=self.items_tree.yview)
        self.items_tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول وشريط التمرير
        self.items_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # ربط الأحداث
        self.items_tree.bind('<Double-1>', self.edit_item)
        self.items_tree.bind('<Delete>', self.delete_item)
        
    def create_totals_section(self, parent):
        """إنشاء قسم الإجماليات"""
        totals_frame = ttk.LabelFrame(parent, text="إجماليات الفاتورة", padding="10")
        totals_frame.pack(fill=tk.X, pady=(0, 10))
        
        # الصف الأول
        row1 = ttk.Frame(totals_frame)
        row1.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(row1, text="الإجمالي قبل الخصم:").pack(side=tk.LEFT)
        self.subtotal_var = tk.StringVar(value="0.00")
        ttk.Label(row1, textvariable=self.subtotal_var, font=('Arial', 12, 'bold')).pack(side=tk.LEFT, padx=(5, 20))
        
        ttk.Label(row1, text="الخصم:").pack(side=tk.LEFT)
        self.discount_var = tk.StringVar(value="0")
        discount_entry = ttk.Entry(row1, textvariable=self.discount_var, width=10)
        discount_entry.pack(side=tk.LEFT, padx=(5, 20))
        discount_entry.bind('<KeyRelease>', self.calculate_totals)
        
        # الصف الثاني
        row2 = ttk.Frame(totals_frame)
        row2.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(row2, text="الضريبة:").pack(side=tk.LEFT)
        self.tax_var = tk.StringVar(value="0")
        tax_entry = ttk.Entry(row2, textvariable=self.tax_var, width=10)
        tax_entry.pack(side=tk.LEFT, padx=(5, 20))
        tax_entry.bind('<KeyRelease>', self.calculate_totals)
        
        ttk.Label(row2, text="الإجمالي النهائي:").pack(side=tk.LEFT)
        self.total_var = tk.StringVar(value="0.00")
        ttk.Label(row2, textvariable=self.total_var, font=('Arial', 14, 'bold'), 
                 foreground='red').pack(side=tk.LEFT, padx=(5, 20))
        
        # الصف الثالث - المدفوع والمتبقي
        row3 = ttk.Frame(totals_frame)
        row3.pack(fill=tk.X)
        
        ttk.Label(row3, text="المدفوع:").pack(side=tk.LEFT)
        self.paid_var = tk.StringVar(value="0")
        paid_entry = ttk.Entry(row3, textvariable=self.paid_var, width=10)
        paid_entry.pack(side=tk.LEFT, padx=(5, 20))
        paid_entry.bind('<KeyRelease>', self.calculate_remaining)
        
        ttk.Label(row3, text="المتبقي:").pack(side=tk.LEFT)
        self.remaining_var = tk.StringVar(value="0.00")
        ttk.Label(row3, textvariable=self.remaining_var, font=('Arial', 12, 'bold'), 
                 foreground='blue').pack(side=tk.LEFT, padx=(5, 20))
        
    def create_control_buttons(self, parent):
        """إنشاء أزرار التحكم"""
        buttons_frame = ttk.Frame(parent)
        buttons_frame.pack(fill=tk.X)
        
        ttk.Button(buttons_frame, text="حفظ الفاتورة", command=self.save_invoice).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="حفظ وطباعة", command=self.save_and_print).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="إلغاء", command=self.cancel).pack(side=tk.RIGHT)
        ttk.Button(buttons_frame, text="فاتورة جديدة", command=self.new_invoice).pack(side=tk.RIGHT, padx=(0, 10))
        
    def setup_keyboard_shortcuts(self):
        """إعداد اختصارات لوحة المفاتيح"""
        self.window.bind('<F1>', lambda e: self.add_item())
        self.window.bind('<F2>', lambda e: self.save_invoice())
        self.window.bind('<F3>', lambda e: self.save_and_print())
        self.window.bind('<Escape>', lambda e: self.cancel())
        self.window.bind('<Control-n>', lambda e: self.new_invoice())
        
    def on_supplier_selected(self, event):
        """عند اختيار مورد"""
        selection = self.supplier_var.get()
        if selection:
            # استخراج اسم المورد
            supplier_name = selection.split(' - ')[0]
            # البحث عن المورد
            for supplier in self.suppliers:
                if supplier.name == supplier_name:
                    self.supplier_info_var.set(f"الرصيد: {supplier.balance:.2f}")
                    break
                    
    def on_product_selected(self, event):
        """عند اختيار منتج"""
        selection = self.product_var.get()
        if selection:
            # استخراج اسم المنتج
            product_name = selection.split(' - ')[0]
            # البحث عن المنتج
            for product in self.products:
                if product.name == product_name:
                    self.unit_price_var.set(str(product.purchase_price))
                    break
                    
    def add_item(self):
        """إضافة صنف للفاتورة"""
        try:
            product_selection = self.product_var.get()
            quantity = float(self.quantity_var.get())
            unit_price = float(self.unit_price_var.get())
            
            if not product_selection or quantity <= 0 or unit_price < 0:
                messagebox.showwarning("تحذير", "يرجى إدخال بيانات صحيحة")
                return
                
            # البحث عن المنتج
            product_name = product_selection.split(' - ')[0]
            selected_product = None
            for product in self.products:
                if product.name == product_name:
                    selected_product = product
                    break
                    
            if not selected_product:
                messagebox.showerror("خطأ", "المنتج غير موجود")
                return
                
            total_price = quantity * unit_price
            
            # إضافة الصنف للجدول
            item_data = (selected_product.name, selected_product.unit, quantity, unit_price, total_price)
            self.items_tree.insert('', tk.END, values=item_data)
            
            # إضافة للقائمة
            self.invoice_items.append({
                'product_id': selected_product.id,
                'product_name': selected_product.name,
                'unit': selected_product.unit,
                'quantity': quantity,
                'unit_price': unit_price,
                'total_price': total_price
            })
            
            # تحديث الإجماليات
            self.calculate_totals()
            
            # مسح الحقول
            self.product_var.set('')
            self.quantity_var.set('1')
            self.unit_price_var.set('')
            
        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال أرقام صحيحة")
            
    def calculate_totals(self, event=None):
        """حساب الإجماليات"""
        try:
            subtotal = sum(item['total_price'] for item in self.invoice_items)
            discount = float(self.discount_var.get() or 0)
            tax = float(self.tax_var.get() or 0)
            
            total = subtotal - discount + tax
            
            self.subtotal_var.set(f"{subtotal:.2f}")
            self.total_var.set(f"{total:.2f}")
            
            self.calculate_remaining()
            
        except ValueError:
            pass
            
    def calculate_remaining(self, event=None):
        """حساب المتبقي"""
        try:
            total = float(self.total_var.get())
            paid = float(self.paid_var.get() or 0)
            remaining = total - paid
            
            self.remaining_var.set(f"{remaining:.2f}")
            
        except ValueError:
            pass
            
    def edit_item(self, event):
        """تعديل صنف"""
        selection = self.items_tree.selection()
        if selection:
            messagebox.showinfo("قريباً", "سيتم تطوير تعديل الأصناف قريباً")
            
    def delete_item(self, event):
        """حذف صنف"""
        selection = self.items_tree.selection()
        if selection:
            if messagebox.askyesno("تأكيد", "هل تريد حذف هذا الصنف؟"):
                item_index = self.items_tree.index(selection[0])
                self.items_tree.delete(selection[0])
                del self.invoice_items[item_index]
                self.calculate_totals()
                
    def add_new_supplier(self):
        """إضافة مورد جديد"""
        messagebox.showinfo("قريباً", "سيتم تطوير إضافة مورد جديد قريباً")
        
    def save_invoice(self):
        """حفظ الفاتورة"""
        if not self.invoice_items:
            messagebox.showwarning("تحذير", "لا يمكن حفظ فاتورة فارغة")
            return
            
        try:
            # إنشاء فاتورة مشتريات جديدة
            invoice_data = {
                'invoice_number': self.invoice_number_var.get(),
                'supplier_id': self.get_selected_supplier_id(),
                'invoice_date': f"{self.invoice_date_var.get()} {self.invoice_time_var.get()}",
                'total_amount': float(self.subtotal_var.get()),
                'discount': float(self.discount_var.get() or 0),
                'tax': float(self.tax_var.get() or 0),
                'final_amount': float(self.total_var.get()),
                'paid_amount': float(self.paid_var.get() or 0),
                'remaining_amount': float(self.remaining_var.get()),
                'payment_method': "نقدي",
                'notes': "",
                'status': "مفتوحة" if float(self.remaining_var.get()) > 0 else "مدفوعة"
            }
            
            # حفظ الفاتورة في قاعدة البيانات
            invoice_id = self.save_purchase_invoice_to_db(invoice_data)
            
            # تحديث رصيد المورد إذا كان هناك متبقي
            if invoice_data['supplier_id'] and invoice_data['remaining_amount'] > 0:
                supplier = Supplier.get_by_id(self.db_manager, invoice_data['supplier_id'])
                if supplier:
                    supplier.update_balance(invoice_data['remaining_amount'], 'add')
            
            messagebox.showinfo("نجح", f"تم حفظ فاتورة المشتريات بنجاح\nرقم الفاتورة: {invoice_data['invoice_number']}")
            
            # إعادة تعيين رقم الفاتورة للفاتورة التالية
            self.invoice_number_var.set(self.db_manager.get_next_invoice_number('purchase'))
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في حفظ الفاتورة: {str(e)}")
            
    def save_purchase_invoice_to_db(self, invoice_data):
        """حفظ فاتورة المشتريات في قاعدة البيانات"""
        # حفظ الفاتورة
        query = """
            INSERT INTO purchase_invoices (invoice_number, supplier_id, invoice_date, 
                                         total_amount, discount, tax, final_amount, 
                                         paid_amount, remaining_amount, payment_method, 
                                         notes, status)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        params = (
            invoice_data['invoice_number'], invoice_data['supplier_id'], invoice_data['invoice_date'],
            invoice_data['total_amount'], invoice_data['discount'], invoice_data['tax'],
            invoice_data['final_amount'], invoice_data['paid_amount'], invoice_data['remaining_amount'],
            invoice_data['payment_method'], invoice_data['notes'], invoice_data['status']
        )
        invoice_id = self.db_manager.execute_insert(query, params)
        
        # حفظ أصناف الفاتورة
        for item in self.invoice_items:
            item_query = """
                INSERT INTO purchase_invoice_items (invoice_id, product_id, quantity, unit_price, total_price)
                VALUES (?, ?, ?, ?, ?)
            """
            item_params = (invoice_id, item['product_id'], item['quantity'], 
                          item['unit_price'], item['total_price'])
            self.db_manager.execute_insert(item_query, item_params)
            
            # تحديث كمية المنتج (زيادة المخزون)
            product = Product.get_by_id(self.db_manager, item['product_id'])
            if product:
                product.update_quantity(item['quantity'], 'add')
                # تحديث سعر الشراء إذا كان مختلفاً
                if product.purchase_price != item['unit_price']:
                    product.purchase_price = item['unit_price']
                    product.save()
                
        return invoice_id
        
    def get_selected_supplier_id(self):
        """الحصول على معرف المورد المحدد"""
        selection = self.supplier_var.get()
        if selection:
            supplier_name = selection.split(' - ')[0]
            for supplier in self.suppliers:
                if supplier.name == supplier_name:
                    return supplier.id
        return None
        
    def save_and_print(self):
        """حفظ وطباعة الفاتورة"""
        self.save_invoice()
        messagebox.showinfo("قريباً", "سيتم تطوير الطباعة قريباً")
        
    def new_invoice(self):
        """فاتورة جديدة"""
        if messagebox.askyesno("تأكيد", "هل تريد إنشاء فاتورة جديدة؟ سيتم فقدان البيانات الحالية"):
            # مسح البيانات
            self.invoice_items.clear()
            for item in self.items_tree.get_children():
                self.items_tree.delete(item)
                
            # إعادة تعيين القيم
            self.invoice_number_var.set(self.db_manager.get_next_invoice_number('purchase'))
            self.invoice_date_var.set(datetime.now().strftime("%Y-%m-%d"))
            self.invoice_time_var.set(datetime.now().strftime("%H:%M:%S"))
            self.supplier_var.set('')
            self.supplier_info_var.set('')
            self.discount_var.set('0')
            self.tax_var.set('0')
            self.paid_var.set('0')
            
            self.calculate_totals()
            
    def cancel(self):
        """إلغاء وإغلاق النافذة"""
        if self.invoice_items:
            if messagebox.askyesno("تأكيد", "هل تريد إغلاق النافذة؟ سيتم فقدان البيانات غير المحفوظة"):
                self.window.destroy()
        else:
            self.window.destroy()
