#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة التحليلات الذكية والتقارير
Smart Analytics and Reports Window
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, timedelta
import sys
import os

# إضافة مسار المجلدات للاستيراد
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'database'))

class AnalyticsWindow:
    """فئة نافذة التحليلات الذكية"""
    
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.window = tk.Toplevel(parent)
        
        self.setup_window()
        self.setup_ui()
        self.load_analytics()
        self.setup_keyboard_shortcuts()
        
    def setup_window(self):
        """إعداد النافذة"""
        self.window.title("التحليلات الذكية والتقارير")
        self.window.geometry("1200x800")
        self.window.resizable(True, True)
        
        # جعل النافذة في المقدمة
        self.window.transient(self.parent)
        self.window.grab_set()
        
        # توسيط النافذة
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (1200 // 2)
        y = (self.window.winfo_screenheight() // 2) - (800 // 2)
        self.window.geometry(f"1200x800+{x}+{y}")
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.window, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # إنشاء دفتر التبويبات
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # تبويب لوحة المعلومات
        self.create_dashboard_tab(notebook)
        
        # تبويب تقارير المبيعات
        self.create_sales_reports_tab(notebook)
        
        # تبويب تقارير المخزون
        self.create_inventory_reports_tab(notebook)
        
        # تبويب التقارير المالية
        self.create_financial_reports_tab(notebook)
        
        # أزرار التحكم
        self.create_control_buttons(main_frame)
        
    def create_dashboard_tab(self, notebook):
        """إنشاء تبويب لوحة المعلومات"""
        dashboard_frame = ttk.Frame(notebook, padding="10")
        notebook.add(dashboard_frame, text="لوحة المعلومات")
        
        # الصف الأول - المؤشرات الرئيسية
        indicators_frame = ttk.LabelFrame(dashboard_frame, text="المؤشرات الرئيسية", padding="10")
        indicators_frame.pack(fill=tk.X, pady=(0, 10))
        
        # إنشاء 4 مؤشرات في صف واحد
        indicators_row = ttk.Frame(indicators_frame)
        indicators_row.pack(fill=tk.X)
        
        # مؤشر إجمالي المبيعات
        sales_frame = ttk.Frame(indicators_row, relief=tk.RAISED, borderwidth=1)
        sales_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        ttk.Label(sales_frame, text="إجمالي المبيعات", font=('Arial', 10, 'bold')).pack(pady=5)
        self.total_sales_var = tk.StringVar(value="0.00 ج.م")
        ttk.Label(sales_frame, textvariable=self.total_sales_var, font=('Arial', 14, 'bold'), 
                 foreground='green').pack(pady=5)
        
        # مؤشر إجمالي المشتريات
        purchases_frame = ttk.Frame(indicators_row, relief=tk.RAISED, borderwidth=1)
        purchases_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        ttk.Label(purchases_frame, text="إجمالي المشتريات", font=('Arial', 10, 'bold')).pack(pady=5)
        self.total_purchases_var = tk.StringVar(value="0.00 ج.م")
        ttk.Label(purchases_frame, textvariable=self.total_purchases_var, font=('Arial', 14, 'bold'), 
                 foreground='red').pack(pady=5)
        
        # مؤشر رصيد الخزنة
        cash_frame = ttk.Frame(indicators_row, relief=tk.RAISED, borderwidth=1)
        cash_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        ttk.Label(cash_frame, text="رصيد الخزنة", font=('Arial', 10, 'bold')).pack(pady=5)
        self.cash_balance_var = tk.StringVar(value="0.00 ج.م")
        ttk.Label(cash_frame, textvariable=self.cash_balance_var, font=('Arial', 14, 'bold'), 
                 foreground='blue').pack(pady=5)
        
        # مؤشر عدد المنتجات
        products_frame = ttk.Frame(indicators_row, relief=tk.RAISED, borderwidth=1)
        products_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        ttk.Label(products_frame, text="عدد المنتجات", font=('Arial', 10, 'bold')).pack(pady=5)
        self.products_count_var = tk.StringVar(value="0")
        ttk.Label(products_frame, textvariable=self.products_count_var, font=('Arial', 14, 'bold'), 
                 foreground='purple').pack(pady=5)
        
        # الصف الثاني - الرسوم البيانية والتحليلات
        charts_frame = ttk.LabelFrame(dashboard_frame, text="التحليلات البصرية", padding="10")
        charts_frame.pack(fill=tk.BOTH, expand=True)
        
        # منطقة الرسوم البيانية (سيتم تطويرها لاحقاً)
        chart_placeholder = ttk.Label(charts_frame, text="سيتم إضافة الرسوم البيانية قريباً\n(مبيعات شهرية، أفضل المنتجات، تحليل الأرباح)", 
                                     font=('Arial', 12), foreground='gray')
        chart_placeholder.pack(expand=True)
        
    def create_sales_reports_tab(self, notebook):
        """إنشاء تبويب تقارير المبيعات"""
        sales_frame = ttk.Frame(notebook, padding="10")
        notebook.add(sales_frame, text="تقارير المبيعات")
        
        # فلاتر التقارير
        filters_frame = ttk.LabelFrame(sales_frame, text="فلاتر التقرير", padding="10")
        filters_frame.pack(fill=tk.X, pady=(0, 10))
        
        # الصف الأول
        row1 = ttk.Frame(filters_frame)
        row1.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(row1, text="من تاريخ:").pack(side=tk.LEFT)
        self.sales_from_date_var = tk.StringVar(value=(datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d"))
        ttk.Entry(row1, textvariable=self.sales_from_date_var, width=12).pack(side=tk.LEFT, padx=(5, 20))
        
        ttk.Label(row1, text="إلى تاريخ:").pack(side=tk.LEFT)
        self.sales_to_date_var = tk.StringVar(value=datetime.now().strftime("%Y-%m-%d"))
        ttk.Entry(row1, textvariable=self.sales_to_date_var, width=12).pack(side=tk.LEFT, padx=(5, 20))
        
        ttk.Button(row1, text="تحديث التقرير", command=self.update_sales_report).pack(side=tk.LEFT, padx=(10, 0))
        
        # جدول تقرير المبيعات
        report_frame = ttk.LabelFrame(sales_frame, text="تقرير المبيعات", padding="5")
        report_frame.pack(fill=tk.BOTH, expand=True)
        
        columns = ('رقم الفاتورة', 'التاريخ', 'العميل', 'الإجمالي', 'المدفوع', 'المتبقي', 'الحالة')
        self.sales_report_tree = ttk.Treeview(report_frame, columns=columns, show='headings', height=15)
        
        # تعريف الأعمدة
        for col in columns:
            self.sales_report_tree.heading(col, text=col)
            
        self.sales_report_tree.column('رقم الفاتورة', width=100, anchor=tk.CENTER)
        self.sales_report_tree.column('التاريخ', width=100, anchor=tk.CENTER)
        self.sales_report_tree.column('العميل', width=150, anchor=tk.W)
        self.sales_report_tree.column('الإجمالي', width=100, anchor=tk.E)
        self.sales_report_tree.column('المدفوع', width=100, anchor=tk.E)
        self.sales_report_tree.column('المتبقي', width=100, anchor=tk.E)
        self.sales_report_tree.column('الحالة', width=80, anchor=tk.CENTER)
        
        # شريط التمرير
        scrollbar_sales = ttk.Scrollbar(report_frame, orient=tk.VERTICAL, command=self.sales_report_tree.yview)
        self.sales_report_tree.configure(yscrollcommand=scrollbar_sales.set)
        
        # تخطيط الجدول
        self.sales_report_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_sales.pack(side=tk.RIGHT, fill=tk.Y)
        
    def create_inventory_reports_tab(self, notebook):
        """إنشاء تبويب تقارير المخزون"""
        inventory_frame = ttk.Frame(notebook, padding="10")
        notebook.add(inventory_frame, text="تقارير المخزون")
        
        # أزرار التقارير
        buttons_frame = ttk.Frame(inventory_frame)
        buttons_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(buttons_frame, text="المخزون الحالي", command=self.current_inventory_report).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="المخزون المنخفض", command=self.low_stock_report).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="حركة المخزون", command=self.inventory_movement_report).pack(side=tk.LEFT, padx=(0, 10))
        
        # جدول تقرير المخزون
        report_frame = ttk.LabelFrame(inventory_frame, text="تقرير المخزون", padding="5")
        report_frame.pack(fill=tk.BOTH, expand=True)
        
        columns = ('اسم المنتج', 'الباركود', 'الوحدة', 'الكمية الحالية', 'الحد الأدنى', 'سعر الشراء', 'سعر البيع', 'قيمة المخزون')
        self.inventory_report_tree = ttk.Treeview(report_frame, columns=columns, show='headings', height=15)
        
        # تعريف الأعمدة
        for col in columns:
            self.inventory_report_tree.heading(col, text=col)
            
        self.inventory_report_tree.column('اسم المنتج', width=150, anchor=tk.W)
        self.inventory_report_tree.column('الباركود', width=100, anchor=tk.CENTER)
        self.inventory_report_tree.column('الوحدة', width=80, anchor=tk.CENTER)
        self.inventory_report_tree.column('الكمية الحالية', width=100, anchor=tk.E)
        self.inventory_report_tree.column('الحد الأدنى', width=80, anchor=tk.E)
        self.inventory_report_tree.column('سعر الشراء', width=100, anchor=tk.E)
        self.inventory_report_tree.column('سعر البيع', width=100, anchor=tk.E)
        self.inventory_report_tree.column('قيمة المخزون', width=120, anchor=tk.E)
        
        # شريط التمرير
        scrollbar_inventory = ttk.Scrollbar(report_frame, orient=tk.VERTICAL, command=self.inventory_report_tree.yview)
        self.inventory_report_tree.configure(yscrollcommand=scrollbar_inventory.set)
        
        # تخطيط الجدول
        self.inventory_report_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_inventory.pack(side=tk.RIGHT, fill=tk.Y)
        
    def create_financial_reports_tab(self, notebook):
        """إنشاء تبويب التقارير المالية"""
        financial_frame = ttk.Frame(notebook, padding="10")
        notebook.add(financial_frame, text="التقارير المالية")
        
        # أزرار التقارير المالية
        buttons_frame = ttk.Frame(financial_frame)
        buttons_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(buttons_frame, text="الأرباح والخسائر", command=self.profit_loss_report).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="كشف العملاء", command=self.customers_statement).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="كشف الموردين", command=self.suppliers_statement).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="التدفق النقدي", command=self.cash_flow_report).pack(side=tk.LEFT, padx=(0, 10))
        
        # منطقة عرض التقرير المالي
        report_text_frame = ttk.LabelFrame(financial_frame, text="التقرير المالي", padding="10")
        report_text_frame.pack(fill=tk.BOTH, expand=True)
        
        # منطقة نص التقرير
        self.financial_report_text = tk.Text(report_text_frame, wrap=tk.WORD, font=('Courier', 10))
        financial_scrollbar = ttk.Scrollbar(report_text_frame, orient=tk.VERTICAL, command=self.financial_report_text.yview)
        self.financial_report_text.configure(yscrollcommand=financial_scrollbar.set)
        
        self.financial_report_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        financial_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
    def create_control_buttons(self, parent):
        """إنشاء أزرار التحكم"""
        buttons_frame = ttk.Frame(parent)
        buttons_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(buttons_frame, text="إغلاق", command=self.close_window).pack(side=tk.RIGHT)
        ttk.Button(buttons_frame, text="طباعة التقرير", command=self.print_report).pack(side=tk.RIGHT, padx=(0, 10))
        ttk.Button(buttons_frame, text="تصدير Excel", command=self.export_excel).pack(side=tk.RIGHT, padx=(0, 10))
        ttk.Button(buttons_frame, text="تحديث البيانات", command=self.load_analytics).pack(side=tk.RIGHT, padx=(0, 10))
        
    def setup_keyboard_shortcuts(self):
        """إعداد اختصارات لوحة المفاتيح"""
        self.window.bind('<F5>', lambda e: self.load_analytics())
        self.window.bind('<F1>', lambda e: self.current_inventory_report())
        self.window.bind('<F2>', lambda e: self.profit_loss_report())
        self.window.bind('<Escape>', lambda e: self.close_window())
        
    def load_analytics(self):
        """تحميل التحليلات الأساسية"""
        self.load_dashboard_data()
        self.update_sales_report()
        self.current_inventory_report()
        
    def load_dashboard_data(self):
        """تحميل بيانات لوحة المعلومات"""
        try:
            # إجمالي المبيعات
            sales_query = "SELECT SUM(final_amount) FROM sales_invoices"
            sales_result = self.db_manager.execute_query(sales_query)
            total_sales = sales_result[0][0] if sales_result and sales_result[0][0] else 0
            self.total_sales_var.set(f"{total_sales:.2f} ج.م")
            
            # إجمالي المشتريات
            purchases_query = "SELECT SUM(final_amount) FROM purchase_invoices"
            purchases_result = self.db_manager.execute_query(purchases_query)
            total_purchases = purchases_result[0][0] if purchases_result and purchases_result[0][0] else 0
            self.total_purchases_var.set(f"{total_purchases:.2f} ج.م")
            
            # رصيد الخزنة
            cash_query = """
                SELECT 
                    SUM(CASE WHEN transaction_type = 'cash_in' THEN amount ELSE 0 END) -
                    SUM(CASE WHEN transaction_type = 'cash_out' THEN amount ELSE 0 END) as balance
                FROM cash_bank_transactions 
                WHERE transaction_type IN ('cash_in', 'cash_out')
            """
            cash_result = self.db_manager.execute_query(cash_query)
            cash_balance = cash_result[0][0] if cash_result and cash_result[0][0] else 0
            self.cash_balance_var.set(f"{cash_balance:.2f} ج.م")
            
            # عدد المنتجات
            products_query = "SELECT COUNT(*) FROM products"
            products_result = self.db_manager.execute_query(products_query)
            products_count = products_result[0][0] if products_result else 0
            self.products_count_var.set(str(products_count))
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل بيانات لوحة المعلومات: {str(e)}")
            
    def update_sales_report(self):
        """تحديث تقرير المبيعات"""
        try:
            # مسح البيانات الحالية
            for item in self.sales_report_tree.get_children():
                self.sales_report_tree.delete(item)
                
            # تحميل فواتير المبيعات
            query = """
                SELECT si.invoice_number, si.invoice_date, c.name as customer_name,
                       si.final_amount, si.paid_amount, si.remaining_amount, si.status
                FROM sales_invoices si
                LEFT JOIN customers c ON si.customer_id = c.id
                WHERE DATE(si.invoice_date) BETWEEN ? AND ?
                ORDER BY si.invoice_date DESC
            """
            params = (self.sales_from_date_var.get(), self.sales_to_date_var.get())
            results = self.db_manager.execute_query(query, params)
            
            for row in results:
                self.sales_report_tree.insert('', tk.END, values=(
                    row['invoice_number'],
                    row['invoice_date'][:10],  # التاريخ فقط
                    row['customer_name'] or 'عميل نقدي',
                    f"{row['final_amount']:.2f}",
                    f"{row['paid_amount']:.2f}",
                    f"{row['remaining_amount']:.2f}",
                    row['status']
                ))
                
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحديث تقرير المبيعات: {str(e)}")
            
    def current_inventory_report(self):
        """تقرير المخزون الحالي"""
        try:
            # مسح البيانات الحالية
            for item in self.inventory_report_tree.get_children():
                self.inventory_report_tree.delete(item)
                
            # تحميل بيانات المخزون
            query = "SELECT * FROM products ORDER BY name"
            results = self.db_manager.execute_query(query)
            
            for row in results:
                inventory_value = row['quantity'] * row['purchase_price']
                
                # تلوين المنتجات منخفضة المخزون
                tags = ()
                if row['quantity'] <= row['min_quantity']:
                    tags = ('low_stock',)
                    
                self.inventory_report_tree.insert('', tk.END, values=(
                    row['name'],
                    row['barcode'],
                    row['unit'],
                    f"{row['quantity']:.2f}",
                    f"{row['min_quantity']:.2f}",
                    f"{row['purchase_price']:.2f}",
                    f"{row['sale_price']:.2f}",
                    f"{inventory_value:.2f}"
                ), tags=tags)
                
            # تكوين ألوان المخزون المنخفض
            self.inventory_report_tree.tag_configure('low_stock', background='#ffcccc')
                
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تقرير المخزون: {str(e)}")
            
    def low_stock_report(self):
        """تقرير المخزون المنخفض"""
        try:
            # مسح البيانات الحالية
            for item in self.inventory_report_tree.get_children():
                self.inventory_report_tree.delete(item)
                
            # تحميل المنتجات منخفضة المخزون
            query = "SELECT * FROM products WHERE quantity <= min_quantity ORDER BY name"
            results = self.db_manager.execute_query(query)
            
            for row in results:
                inventory_value = row['quantity'] * row['purchase_price']
                
                self.inventory_report_tree.insert('', tk.END, values=(
                    row['name'],
                    row['barcode'],
                    row['unit'],
                    f"{row['quantity']:.2f}",
                    f"{row['min_quantity']:.2f}",
                    f"{row['purchase_price']:.2f}",
                    f"{row['sale_price']:.2f}",
                    f"{inventory_value:.2f}"
                ), tags=('low_stock',))
                
            self.inventory_report_tree.tag_configure('low_stock', background='#ffcccc')
            
            if not results:
                messagebox.showinfo("معلومات", "لا توجد منتجات منخفضة المخزون")
                
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تقرير المخزون المنخفض: {str(e)}")
            
    def inventory_movement_report(self):
        """تقرير حركة المخزون"""
        messagebox.showinfo("قريباً", "سيتم تطوير تقرير حركة المخزون قريباً")
        
    def profit_loss_report(self):
        """تقرير الأرباح والخسائر"""
        try:
            # مسح النص الحالي
            self.financial_report_text.delete(1.0, tk.END)
            
            # إنشاء تقرير الأرباح والخسائر
            report = "تقرير الأرباح والخسائر\n"
            report += "=" * 50 + "\n\n"
            
            # المبيعات
            sales_query = "SELECT SUM(final_amount) FROM sales_invoices"
            sales_result = self.db_manager.execute_query(sales_query)
            total_sales = sales_result[0][0] if sales_result and sales_result[0][0] else 0
            
            # المشتريات
            purchases_query = "SELECT SUM(final_amount) FROM purchase_invoices"
            purchases_result = self.db_manager.execute_query(purchases_query)
            total_purchases = purchases_result[0][0] if purchases_result and purchases_result[0][0] else 0
            
            # حساب الربح الإجمالي
            gross_profit = total_sales - total_purchases
            
            report += f"إجمالي المبيعات:        {total_sales:>15.2f} ج.م\n"
            report += f"إجمالي المشتريات:      {total_purchases:>15.2f} ج.م\n"
            report += "-" * 50 + "\n"
            report += f"الربح الإجمالي:         {gross_profit:>15.2f} ج.م\n\n"
            
            if total_sales > 0:
                profit_margin = (gross_profit / total_sales) * 100
                report += f"هامش الربح:            {profit_margin:>15.2f}%\n"
            
            # إدراج التقرير
            self.financial_report_text.insert(tk.END, report)
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تقرير الأرباح والخسائر: {str(e)}")
            
    def customers_statement(self):
        """كشف العملاء"""
        messagebox.showinfo("قريباً", "سيتم تطوير كشف العملاء قريباً")
        
    def suppliers_statement(self):
        """كشف الموردين"""
        messagebox.showinfo("قريباً", "سيتم تطوير كشف الموردين قريباً")
        
    def cash_flow_report(self):
        """تقرير التدفق النقدي"""
        messagebox.showinfo("قريباً", "سيتم تطوير تقرير التدفق النقدي قريباً")
        
    def print_report(self):
        """طباعة التقرير"""
        messagebox.showinfo("قريباً", "سيتم تطوير طباعة التقارير قريباً")
        
    def export_excel(self):
        """تصدير إلى Excel"""
        messagebox.showinfo("قريباً", "سيتم تطوير تصدير Excel قريباً")
        
    def close_window(self):
        """إغلاق النافذة"""
        self.window.destroy()
