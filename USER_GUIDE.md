# دليل المستخدم - نظام المحاسبة المتكامل

## مقدمة

مرحباً بك في نظام المحاسبة المتكامل، الحل الشامل لإدارة أعمالك المحاسبية بكفاءة واحترافية.

## البدء السريع

### 1. تشغيل النظام لأول مرة

1. تأكد من تثبيت Python 3.7 أو أحدث
2. قم بتشغيل `setup.bat` لتثبيت المتطلبات
3. قم بتشغيل `run.bat` أو `python main.py`

### 2. الواجهة الرئيسية

عند تشغيل النظام، ستظهر لك النافذة الرئيسية التي تحتوي على:

- **شريط القوائم**: يحتوي على جميع وظائف النظام
- **شريط الأدوات**: أزرار سريعة للوظائف الأساسية
- **منطقة الترحيب**: معلومات وإحصائيات سريعة
- **شريط الحالة**: معلومات حالة النظام

## الوظائف الأساسية

### إدارة العملاء

#### إضافة عميل جديد
1. اضغط F3 أو اختر "إدارة العملاء" من القائمة
2. اضغط "عميل جديد"
3. أدخل البيانات المطلوبة:
   - الاسم (مطلوب)
   - الهاتف
   - العنوان
   - البريد الإلكتروني
   - الرصيد الابتدائي
   - الملاحظات
4. اضغط "حفظ"

#### البحث عن عميل
1. في نافذة إدارة العملاء
2. أدخل اسم العميل أو رقم الهاتف في حقل البحث
3. اضغط Enter أو "بحث"

#### تعديل بيانات عميل
1. اختر العميل من القائمة
2. عدل البيانات في الجانب الأيمن
3. اضغط "حفظ"

### إدارة المنتجات

#### إضافة منتج جديد
1. اضغط F5 أو اختر "إدارة المنتجات"
2. اضغط "منتج جديد"
3. أدخل البيانات:
   - اسم المنتج (مطلوب)
   - الباركود
   - الوحدة (قطعة، كيلو، إلخ)
   - الفئة
   - سعر الشراء
   - سعر البيع
   - الكمية الحالية
   - الحد الأدنى للكمية
4. اضغط "حفظ"

#### مراقبة المخزون المنخفض
1. في نافذة إدارة المنتجات
2. اضغط "مخزون منخفض"
3. ستظهر المنتجات التي وصلت للحد الأدنى

### إنشاء فاتورة مبيعات

#### خطوات إنشاء الفاتورة
1. اضغط F1 أو اختر "فاتورة مبيعات جديدة"
2. اختر العميل (اختياري للمبيعات النقدية)
3. أضف المنتجات:
   - اختر المنتج من القائمة
   - أدخل الكمية
   - تأكد من السعر
   - اضغط "إضافة"
4. أدخل الخصم والضريبة إن وجدت
5. أدخل المبلغ المدفوع
6. اضغط "حفظ الفاتورة"

#### طباعة الفاتورة
- بعد الحفظ، اضغط "حفظ وطباعة"
- أو استخدم "طباعة" من قائمة الملف

### إدارة الخزنة

#### إيداع في الخزنة
1. اضغط F7 أو اختر "إدارة الخزنة"
2. في تبويب "الخزنة"
3. اختر "إيداع"
4. أدخل المبلغ والوصف
5. اضغط "تنفيذ العملية"

#### سحب من الخزنة
1. نفس الخطوات السابقة
2. اختر "سحب" بدلاً من "إيداع"

#### إضافة حساب مصرفي
1. في تبويب "الحسابات المصرفية"
2. أدخل بيانات الحساب:
   - اسم الحساب
   - اسم البنك
   - رقم الحساب
   - الرصيد الابتدائي
3. اضغط "حفظ"

### التقارير والتحليلات

#### عرض لوحة المعلومات
1. اضغط F9 أو اختر "التحليلات الذكية"
2. في تبويب "لوحة المعلومات"
3. ستجد المؤشرات الرئيسية:
   - إجمالي المبيعات
   - إجمالي المشتريات
   - رصيد الخزنة
   - عدد المنتجات

#### تقرير المبيعات
1. في تبويب "تقارير المبيعات"
2. حدد الفترة الزمنية
3. اضغط "تحديث التقرير"

#### تقرير المخزون
1. في تبويب "تقارير المخزون"
2. اختر نوع التقرير:
   - المخزون الحالي
   - المخزون المنخفض
   - حركة المخزون

#### التقارير المالية
1. في تبويب "التقارير المالية"
2. اختر نوع التقرير:
   - الأرباح والخسائر
   - كشف العملاء
   - كشف الموردين
   - التدفق النقدي

## اختصارات لوحة المفاتيح

### الاختصارات الرئيسية
- **F1**: فاتورة مبيعات جديدة
- **F2**: فاتورة مشتريات جديدة
- **F3**: إدارة العملاء
- **F4**: إدارة الموردين
- **F5**: إدارة المنتجات
- **F6**: قارئ الباركود
- **F7**: إدارة الخزنة
- **F8**: إدارة الحسابات المصرفية
- **F9**: التحليلات الذكية

### اختصارات الملفات
- **Ctrl+N**: ملف جديد
- **Ctrl+O**: فتح ملف
- **Ctrl+S**: حفظ ملف
- **Ctrl+P**: طباعة
- **Alt+F4**: خروج

### اختصارات التنقل
- **Escape**: إلغاء/إغلاق النافذة
- **Enter**: تأكيد/موافق
- **Tab**: الانتقال للحقل التالي
- **Shift+Tab**: الانتقال للحقل السابق

## نصائح مهمة

### الأمان والنسخ الاحتياطي
1. **النسخ الاحتياطي**: يتم إنشاء نسخة احتياطية تلقائياً كل أسبوع
2. **حفظ البيانات**: احفظ عملك بانتظام باستخدام Ctrl+S
3. **إغلاق آمن**: استخدم "خروج" من القائمة بدلاً من إغلاق النافذة مباشرة

### تحسين الأداء
1. **تنظيف البيانات**: احذف البيانات القديمة غير المطلوبة
2. **فهرسة البحث**: استخدم البحث بدلاً من التمرير في القوائم الطويلة
3. **إغلاق النوافذ**: أغلق النوافذ غير المستخدمة

### استكشاف الأخطاء
1. **خطأ في قاعدة البيانات**: أعد تشغيل التطبيق
2. **بطء في الأداء**: أغلق النوافذ غير المطلوبة
3. **مشكلة في الطباعة**: تأكد من تشغيل الطابعة
4. **خطأ في الحفظ**: تأكد من صحة البيانات المدخلة

## الدعم الفني

### الحصول على المساعدة
1. راجع هذا الدليل أولاً
2. استخدم "مساعدة" من القائمة الرئيسية
3. تحقق من ملف README.md للمعلومات التقنية

### الإبلاغ عن الأخطاء
عند مواجهة خطأ، يرجى تسجيل:
1. الخطوات التي أدت للخطأ
2. رسالة الخطأ (إن وجدت)
3. إصدار النظام
4. نظام التشغيل المستخدم

### طلب ميزات جديدة
نرحب بطلباتكم لإضافة ميزات جديدة أو تحسين الموجود.

---

**نتمنى لك تجربة ممتعة ومثمرة مع نظام المحاسبة المتكامل!**
