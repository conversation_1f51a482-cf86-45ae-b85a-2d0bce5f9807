#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
النافذة الرئيسية للتطبيق
Main Application Window
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
import os

class MainWindow:
    """فئة النافذة الرئيسية"""
    
    def __init__(self, root, db_manager):
        self.root = root
        self.db_manager = db_manager
        self.setup_ui()
        self.setup_keyboard_shortcuts()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إنشاء القائمة الرئيسية
        self.create_menu_bar()
        
        # إنشاء شريط الأدوات
        self.create_toolbar()
        
        # إنشاء المنطقة الرئيسية
        self.create_main_area()
        
        # إنشاء شريط الحالة
        self.create_status_bar()
        
    def create_menu_bar(self):
        """إنشاء شريط القوائم"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # قائمة الملف
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="ملف", menu=file_menu)
        file_menu.add_command(label="جديد", command=self.new_file, accelerator="Ctrl+N")
        file_menu.add_command(label="فتح", command=self.open_file, accelerator="Ctrl+O")
        file_menu.add_command(label="حفظ", command=self.save_file, accelerator="Ctrl+S")
        file_menu.add_separator()
        file_menu.add_command(label="خروج", command=self.exit_app, accelerator="Alt+F4")
        
        # قائمة المبيعات
        sales_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="المبيعات", menu=sales_menu)
        sales_menu.add_command(label="فاتورة مبيعات جديدة", command=self.new_sales_invoice, accelerator="F1")
        sales_menu.add_command(label="عرض فواتير المبيعات", command=self.view_sales_invoices)
        sales_menu.add_command(label="تقرير المبيعات", command=self.sales_report)
        
        # قائمة المشتريات
        purchase_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="المشتريات", menu=purchase_menu)
        purchase_menu.add_command(label="فاتورة مشتريات جديدة", command=self.new_purchase_invoice, accelerator="F2")
        purchase_menu.add_command(label="عرض فواتير المشتريات", command=self.view_purchase_invoices)
        purchase_menu.add_command(label="تقرير المشتريات", command=self.purchase_report)
        
        # قائمة العملاء والموردين
        contacts_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="العملاء والموردين", menu=contacts_menu)
        contacts_menu.add_command(label="إدارة العملاء", command=self.manage_customers, accelerator="F3")
        contacts_menu.add_command(label="إدارة الموردين", command=self.manage_suppliers, accelerator="F4")
        
        # قائمة المخزون
        inventory_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="المخزون", menu=inventory_menu)
        inventory_menu.add_command(label="إدارة المنتجات", command=self.manage_products, accelerator="F5")
        inventory_menu.add_command(label="تقرير المخزون", command=self.inventory_report)
        inventory_menu.add_command(label="قارئ الباركود", command=self.barcode_scanner, accelerator="F6")
        
        # قائمة الخزنة والبنوك
        cash_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="الخزنة والبنوك", menu=cash_menu)
        cash_menu.add_command(label="إدارة الخزنة", command=self.manage_cash, accelerator="F7")
        cash_menu.add_command(label="إدارة الحسابات المصرفية", command=self.manage_bank_accounts, accelerator="F8")
        cash_menu.add_command(label="التحويلات البنكية", command=self.bank_transfers)
        cash_menu.add_command(label="المدفوعات", command=self.manage_payments)
        
        # قائمة التقارير
        reports_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="التقارير", menu=reports_menu)
        reports_menu.add_command(label="التحليلات الذكية", command=self.smart_analytics, accelerator="F9")
        reports_menu.add_command(label="تقرير الأرباح والخسائر", command=self.profit_loss_report)
        reports_menu.add_command(label="تقرير الميزانية", command=self.balance_sheet)
        reports_menu.add_command(label="تقرير التدفق النقدي", command=self.cash_flow_report)
        
        # قائمة المساعدة
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="مساعدة", menu=help_menu)
        help_menu.add_command(label="دليل المستخدم", command=self.user_guide)
        help_menu.add_command(label="اختصارات لوحة المفاتيح", command=self.keyboard_shortcuts)
        help_menu.add_command(label="حول البرنامج", command=self.about)
        
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar = ttk.Frame(self.root)
        toolbar.pack(side=tk.TOP, fill=tk.X, padx=5, pady=2)
        
        # أزرار سريعة
        ttk.Button(toolbar, text="فاتورة مبيعات", command=self.new_sales_invoice).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="فاتورة مشتريات", command=self.new_purchase_invoice).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="العملاء", command=self.manage_customers).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="الموردين", command=self.manage_suppliers).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="المنتجات", command=self.manage_products).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="الخزنة", command=self.manage_cash).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="التقارير", command=self.smart_analytics).pack(side=tk.LEFT, padx=2)
        
        # فاصل
        ttk.Separator(toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)
        
        # معلومات سريعة
        ttk.Label(toolbar, text="التاريخ:").pack(side=tk.RIGHT, padx=2)
        ttk.Label(toolbar, text=datetime.now().strftime("%Y-%m-%d")).pack(side=tk.RIGHT, padx=2)
        
    def create_main_area(self):
        """إنشاء المنطقة الرئيسية"""
        # إطار رئيسي
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # منطقة الترحيب
        welcome_frame = ttk.LabelFrame(main_frame, text="مرحباً بك في نظام المحاسبة المتكامل", padding=20)
        welcome_frame.pack(fill=tk.BOTH, expand=True)
        
        # رسالة ترحيب
        welcome_text = """
        نظام المحاسبة المتكامل
        ========================
        
        مرحباً بك في نظام المحاسبة الشامل الذي يوفر لك:
        
        • إدارة فواتير المبيعات والمشتريات
        • متابعة العملاء والموردين
        • إدارة المخزون والمنتجات
        • متابعة الخزنة والحسابات المصرفية
        • تقارير مالية شاملة وتحليلات ذكية
        • قارئ الباركود المتطور
        • اختصارات لوحة المفاتيح للعمل السريع
        
        استخدم القوائم أعلاه أو الأزرار السريعة للبدء
        أو استخدم اختصارات لوحة المفاتيح (F1-F9)
        """
        
        text_widget = tk.Text(welcome_frame, wrap=tk.WORD, font=('Arial', 12), 
                             bg='#f0f0f0', relief=tk.FLAT, state=tk.DISABLED)
        text_widget.pack(fill=tk.BOTH, expand=True)
        
        # إدراج النص
        text_widget.config(state=tk.NORMAL)
        text_widget.insert(tk.END, welcome_text)
        text_widget.config(state=tk.DISABLED)
        
        # إطار الإحصائيات السريعة
        stats_frame = ttk.LabelFrame(main_frame, text="إحصائيات سريعة", padding=10)
        stats_frame.pack(fill=tk.X, pady=(10, 0))
        
        # عرض إحصائيات أساسية
        self.update_quick_stats(stats_frame)
        
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        status_frame = ttk.Frame(self.root)
        status_frame.pack(side=tk.BOTTOM, fill=tk.X)
        
        self.status_var = tk.StringVar()
        self.status_var.set("جاهز")
        
        status_label = ttk.Label(status_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_label.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        # معلومات إضافية
        ttk.Label(status_frame, text="نظام المحاسبة المتكامل v1.0", relief=tk.SUNKEN).pack(side=tk.RIGHT)
        
    def update_quick_stats(self, parent):
        """تحديث الإحصائيات السريعة"""
        try:
            # إحصائيات العملاء
            customers_count = self.db_manager.execute_query("SELECT COUNT(*) FROM customers")[0][0]
            
            # إحصائيات الموردين
            suppliers_count = self.db_manager.execute_query("SELECT COUNT(*) FROM suppliers")[0][0]
            
            # إحصائيات المنتجات
            products_count = self.db_manager.execute_query("SELECT COUNT(*) FROM products")[0][0]
            
            # إحصائيات الفواتير
            sales_count = self.db_manager.execute_query("SELECT COUNT(*) FROM sales_invoices")[0][0]
            purchase_count = self.db_manager.execute_query("SELECT COUNT(*) FROM purchase_invoices")[0][0]
            
            # عرض الإحصائيات
            stats_text = f"العملاء: {customers_count} | الموردين: {suppliers_count} | المنتجات: {products_count} | فواتير المبيعات: {sales_count} | فواتير المشتريات: {purchase_count}"
            
            ttk.Label(parent, text=stats_text, font=('Arial', 10)).pack()
            
        except Exception as e:
            ttk.Label(parent, text="خطأ في تحميل الإحصائيات").pack()
            
    def setup_keyboard_shortcuts(self):
        """إعداد اختصارات لوحة المفاتيح"""
        self.root.bind('<Control-n>', lambda e: self.new_file())
        self.root.bind('<Control-o>', lambda e: self.open_file())
        self.root.bind('<Control-s>', lambda e: self.save_file())
        self.root.bind('<F1>', lambda e: self.new_sales_invoice())
        self.root.bind('<F2>', lambda e: self.new_purchase_invoice())
        self.root.bind('<F3>', lambda e: self.manage_customers())
        self.root.bind('<F4>', lambda e: self.manage_suppliers())
        self.root.bind('<F5>', lambda e: self.manage_products())
        self.root.bind('<F6>', lambda e: self.barcode_scanner())
        self.root.bind('<F7>', lambda e: self.manage_cash())
        self.root.bind('<F8>', lambda e: self.manage_bank_accounts())
        self.root.bind('<F9>', lambda e: self.smart_analytics())
        
    # دوال الأحداث (سيتم تطويرها لاحقاً)
    def new_file(self): self.status_var.set("ملف جديد")
    def open_file(self): self.status_var.set("فتح ملف")
    def save_file(self): self.status_var.set("حفظ ملف")
    def exit_app(self): self.root.quit()
    
    def new_sales_invoice(self):
        self.status_var.set("فاتورة مبيعات جديدة")
        try:
            from ui.sales_invoice_window import SalesInvoiceWindow
            SalesInvoiceWindow(self.root, self.db_manager)
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح نافذة فاتورة المبيعات: {str(e)}")
        
    def view_sales_invoices(self):
        self.status_var.set("عرض فواتير المبيعات")
        try:
            from ui.invoices_viewer_window import InvoicesViewerWindow
            InvoicesViewerWindow(self.root, self.db_manager, 'sales')
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح نافذة عرض فواتير المبيعات: {str(e)}")

    def sales_report(self): self.status_var.set("تقرير المبيعات")
    
    def new_purchase_invoice(self):
        self.status_var.set("فاتورة مشتريات جديدة")
        try:
            from ui.purchase_invoice_window import PurchaseInvoiceWindow
            PurchaseInvoiceWindow(self.root, self.db_manager)
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح نافذة فاتورة المشتريات: {str(e)}")
        
    def view_purchase_invoices(self):
        self.status_var.set("عرض فواتير المشتريات")
        try:
            from ui.invoices_viewer_window import InvoicesViewerWindow
            InvoicesViewerWindow(self.root, self.db_manager, 'purchase')
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح نافذة عرض فواتير المشتريات: {str(e)}")

    def purchase_report(self): self.status_var.set("تقرير المشتريات")
    
    def manage_customers(self):
        self.status_var.set("إدارة العملاء")
        try:
            from ui.customers_window import CustomersWindow
            CustomersWindow(self.root, self.db_manager)
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح نافذة إدارة العملاء: {str(e)}")
        
    def manage_suppliers(self):
        self.status_var.set("إدارة الموردين")
        try:
            from ui.suppliers_window import SuppliersWindow
            SuppliersWindow(self.root, self.db_manager)
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح نافذة إدارة الموردين: {str(e)}")
        
    def manage_products(self):
        self.status_var.set("إدارة المنتجات")
        try:
            from ui.products_window import ProductsWindow
            ProductsWindow(self.root, self.db_manager)
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح نافذة إدارة المنتجات: {str(e)}")
        
    def inventory_report(self): self.status_var.set("تقرير المخزون")
    def barcode_scanner(self):
        self.status_var.set("قارئ الباركود")
        try:
            from ui.barcode_scanner_window import BarcodeScannerWindow
            BarcodeScannerWindow(self.root, self.db_manager)
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح نافذة قارئ الباركود: {str(e)}")
        
    def manage_cash(self):
        self.status_var.set("إدارة الخزنة")
        try:
            from ui.cash_management_window import CashManagementWindow
            CashManagementWindow(self.root, self.db_manager)
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح نافذة إدارة الخزنة: {str(e)}")
        
    def manage_bank_accounts(self):
        self.status_var.set("إدارة الحسابات المصرفية")
        try:
            from ui.bank_accounts_window import BankAccountsWindow
            BankAccountsWindow(self.root, self.db_manager)
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح نافذة إدارة الحسابات المصرفية: {str(e)}")
        
    def bank_transfers(self): self.status_var.set("التحويلات البنكية")
    def manage_payments(self): self.status_var.set("المدفوعات")
    
    def smart_analytics(self):
        self.status_var.set("التحليلات الذكية")
        try:
            from ui.analytics_window import AnalyticsWindow
            AnalyticsWindow(self.root, self.db_manager)
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح نافذة التحليلات الذكية: {str(e)}")
        
    def profit_loss_report(self): self.status_var.set("تقرير الأرباح والخسائر")
    def balance_sheet(self): self.status_var.set("تقرير الميزانية")
    def cash_flow_report(self): self.status_var.set("تقرير التدفق النقدي")
    
    def user_guide(self): messagebox.showinfo("دليل المستخدم", "دليل المستخدم قيد التطوير")
    def keyboard_shortcuts(self): 
        shortcuts_text = """
        اختصارات لوحة المفاتيح:
        
        F1 - فاتورة مبيعات جديدة
        F2 - فاتورة مشتريات جديدة
        F3 - إدارة العملاء
        F4 - إدارة الموردين
        F5 - إدارة المنتجات
        F6 - قارئ الباركود
        F7 - إدارة الخزنة
        F8 - إدارة الحسابات المصرفية
        F9 - التحليلات الذكية
        
        Ctrl+N - ملف جديد
        Ctrl+O - فتح ملف
        Ctrl+S - حفظ ملف
        Alt+F4 - خروج
        """
        messagebox.showinfo("اختصارات لوحة المفاتيح", shortcuts_text)
        
    def about(self): 
        about_text = """
        نظام المحاسبة المتكامل
        الإصدار 1.0
        
        نظام محاسبة شامل ومتكامل
        يوفر جميع الأدوات اللازمة لإدارة الأعمال
        
        تم التطوير باستخدام Python و tkinter
        مع دعم كامل للغة العربية
        """
        messagebox.showinfo("حول البرنامج", about_text)
