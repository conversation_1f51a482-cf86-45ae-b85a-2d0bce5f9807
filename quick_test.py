#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع للنظام
Quick System Test
"""

print("=" * 50)
print("اختبار سريع لنظام المحاسبة المتكامل")
print("Quick Test for Accounting System")
print("=" * 50)

# اختبار الاستيرادات الأساسية
try:
    import tkinter as tk
    print("✓ tkinter - متوفر")
except ImportError:
    print("✗ tkinter - غير متوفر")

try:
    import sqlite3
    print("✓ sqlite3 - متوفر")
except ImportError:
    print("✗ sqlite3 - غير متوفر")

try:
    from datetime import datetime
    print("✓ datetime - متوفر")
except ImportError:
    print("✗ datetime - غير متوفر")

# اختبار قاعدة البيانات
try:
    from database.db_manager import DatabaseManager
    db = DatabaseManager()
    print("✓ قاعدة البيانات - تم الاتصال")
except Exception as e:
    print(f"✗ قاعدة البيانات - خطأ: {str(e)}")

# اختبار النافذة الرئيسية
try:
    from ui.main_window import MainWindow
    print("✓ النافذة الرئيسية - تم الاستيراد")
except Exception as e:
    print(f"✗ النافذة الرئيسية - خطأ: {str(e)}")

print("\n" + "=" * 50)
print("انتهى الاختبار السريع")
print("Quick test completed")
print("=" * 50)
