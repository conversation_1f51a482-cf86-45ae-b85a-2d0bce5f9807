#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة إدارة الخزنة والحسابات المصرفية
Cash and Bank Management Window
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
import sys
import os

# إضافة مسار المجلدات للاستيراد
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'database'))

class CashManagementWindow:
    """فئة نافذة إدارة الخزنة والحسابات المصرفية"""
    
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.window = tk.Toplevel(parent)
        self.bank_accounts = []
        self.transactions = []
        
        self.setup_window()
        self.setup_ui()
        self.load_data()
        self.setup_keyboard_shortcuts()
        
    def setup_window(self):
        """إعداد النافذة"""
        self.window.title("إدارة الخزنة والحسابات المصرفية")
        self.window.geometry("1100x700")
        self.window.resizable(True, True)
        
        # جعل النافذة في المقدمة
        self.window.transient(self.parent)
        self.window.grab_set()
        
        # توسيط النافذة
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (1100 // 2)
        y = (self.window.winfo_screenheight() // 2) - (700 // 2)
        self.window.geometry(f"1100x700+{x}+{y}")
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.window, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # إنشاء دفتر التبويبات
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # تبويب الخزنة
        self.create_cash_tab(notebook)
        
        # تبويب الحسابات المصرفية
        self.create_bank_accounts_tab(notebook)
        
        # تبويب الحركات
        self.create_transactions_tab(notebook)
        
        # أزرار التحكم
        self.create_control_buttons(main_frame)
        
    def create_cash_tab(self, notebook):
        """إنشاء تبويب الخزنة"""
        cash_frame = ttk.Frame(notebook, padding="10")
        notebook.add(cash_frame, text="الخزنة")
        
        # معلومات الخزنة
        info_frame = ttk.LabelFrame(cash_frame, text="معلومات الخزنة", padding="10")
        info_frame.pack(fill=tk.X, pady=(0, 10))
        
        # رصيد الخزنة
        ttk.Label(info_frame, text="رصيد الخزنة الحالي:", font=('Arial', 12, 'bold')).pack(side=tk.LEFT)
        self.cash_balance_var = tk.StringVar()
        ttk.Label(info_frame, textvariable=self.cash_balance_var, font=('Arial', 14, 'bold'), 
                 foreground='green').pack(side=tk.LEFT, padx=(10, 0))
        
        # عمليات الخزنة
        operations_frame = ttk.LabelFrame(cash_frame, text="عمليات الخزنة", padding="10")
        operations_frame.pack(fill=tk.X, pady=(0, 10))
        
        # الصف الأول
        row1 = ttk.Frame(operations_frame)
        row1.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(row1, text="نوع العملية:").pack(side=tk.LEFT)
        self.cash_operation_var = tk.StringVar(value="إيداع")
        operation_combo = ttk.Combobox(row1, textvariable=self.cash_operation_var, width=15)
        operation_combo['values'] = ('إيداع', 'سحب')
        operation_combo.pack(side=tk.LEFT, padx=(5, 20))
        
        ttk.Label(row1, text="المبلغ:").pack(side=tk.LEFT)
        self.cash_amount_var = tk.StringVar()
        ttk.Entry(row1, textvariable=self.cash_amount_var, width=15).pack(side=tk.LEFT, padx=(5, 20))
        
        # الصف الثاني
        row2 = ttk.Frame(operations_frame)
        row2.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(row2, text="الوصف:").pack(side=tk.LEFT)
        self.cash_description_var = tk.StringVar()
        ttk.Entry(row2, textvariable=self.cash_description_var, width=50).pack(side=tk.LEFT, padx=(5, 20))
        
        # أزرار العمليات
        buttons_frame = ttk.Frame(operations_frame)
        buttons_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(buttons_frame, text="تنفيذ العملية", command=self.execute_cash_operation).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="مسح", command=self.clear_cash_form).pack(side=tk.LEFT)
        
    def create_bank_accounts_tab(self, notebook):
        """إنشاء تبويب الحسابات المصرفية"""
        bank_frame = ttk.Frame(notebook, padding="10")
        notebook.add(bank_frame, text="الحسابات المصرفية")
        
        # قائمة الحسابات
        accounts_frame = ttk.LabelFrame(bank_frame, text="الحسابات المصرفية", padding="5")
        accounts_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        # جدول الحسابات
        columns = ('اسم الحساب', 'البنك', 'رقم الحساب', 'الرصيد')
        self.accounts_tree = ttk.Treeview(accounts_frame, columns=columns, show='headings', height=12)
        
        # تعريف الأعمدة
        for col in columns:
            self.accounts_tree.heading(col, text=col)
            
        self.accounts_tree.column('اسم الحساب', width=150, anchor=tk.W)
        self.accounts_tree.column('البنك', width=120, anchor=tk.W)
        self.accounts_tree.column('رقم الحساب', width=120, anchor=tk.CENTER)
        self.accounts_tree.column('الرصيد', width=100, anchor=tk.E)
        
        # شريط التمرير
        scrollbar_accounts = ttk.Scrollbar(accounts_frame, orient=tk.VERTICAL, command=self.accounts_tree.yview)
        self.accounts_tree.configure(yscrollcommand=scrollbar_accounts.set)
        
        # تخطيط الجدول
        self.accounts_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_accounts.pack(side=tk.RIGHT, fill=tk.Y)
        
        # تفاصيل الحساب
        details_frame = ttk.LabelFrame(bank_frame, text="تفاصيل الحساب", padding="10")
        details_frame.pack(side=tk.RIGHT, fill=tk.Y)
        
        # اسم الحساب
        ttk.Label(details_frame, text="اسم الحساب:").grid(row=0, column=0, sticky=tk.W, pady=(0, 10))
        self.account_name_var = tk.StringVar()
        ttk.Entry(details_frame, textvariable=self.account_name_var, width=25).grid(row=0, column=1, pady=(0, 10))
        
        # البنك
        ttk.Label(details_frame, text="البنك:").grid(row=1, column=0, sticky=tk.W, pady=(0, 10))
        self.bank_name_var = tk.StringVar()
        ttk.Entry(details_frame, textvariable=self.bank_name_var, width=25).grid(row=1, column=1, pady=(0, 10))
        
        # رقم الحساب
        ttk.Label(details_frame, text="رقم الحساب:").grid(row=2, column=0, sticky=tk.W, pady=(0, 10))
        self.account_number_var = tk.StringVar()
        ttk.Entry(details_frame, textvariable=self.account_number_var, width=25).grid(row=2, column=1, pady=(0, 10))
        
        # الرصيد الابتدائي
        ttk.Label(details_frame, text="الرصيد الابتدائي:").grid(row=3, column=0, sticky=tk.W, pady=(0, 10))
        self.initial_balance_var = tk.StringVar()
        ttk.Entry(details_frame, textvariable=self.initial_balance_var, width=25).grid(row=3, column=1, pady=(0, 10))
        
        # الملاحظات
        ttk.Label(details_frame, text="الملاحظات:").grid(row=4, column=0, sticky=tk.W+tk.N, pady=(0, 10))
        self.account_notes_text = tk.Text(details_frame, width=25, height=4)
        self.account_notes_text.grid(row=4, column=1, pady=(0, 10))
        
        # أزرار الحسابات
        account_buttons = ttk.Frame(details_frame)
        account_buttons.grid(row=5, column=0, columnspan=2, pady=(20, 0))
        
        ttk.Button(account_buttons, text="حساب جديد", command=self.new_account).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(account_buttons, text="حفظ", command=self.save_account).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(account_buttons, text="حذف", command=self.delete_account).pack(side=tk.LEFT)
        
    def create_transactions_tab(self, notebook):
        """إنشاء تبويب الحركات"""
        trans_frame = ttk.Frame(notebook, padding="10")
        notebook.add(trans_frame, text="الحركات")
        
        # فلاتر البحث
        filter_frame = ttk.LabelFrame(trans_frame, text="فلاتر البحث", padding="10")
        filter_frame.pack(fill=tk.X, pady=(0, 10))
        
        # الصف الأول
        row1 = ttk.Frame(filter_frame)
        row1.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(row1, text="من تاريخ:").pack(side=tk.LEFT)
        self.from_date_var = tk.StringVar(value=datetime.now().strftime("%Y-%m-%d"))
        ttk.Entry(row1, textvariable=self.from_date_var, width=12).pack(side=tk.LEFT, padx=(5, 20))
        
        ttk.Label(row1, text="إلى تاريخ:").pack(side=tk.LEFT)
        self.to_date_var = tk.StringVar(value=datetime.now().strftime("%Y-%m-%d"))
        ttk.Entry(row1, textvariable=self.to_date_var, width=12).pack(side=tk.LEFT, padx=(5, 20))
        
        ttk.Button(row1, text="بحث", command=self.search_transactions).pack(side=tk.LEFT, padx=(10, 0))
        
        # جدول الحركات
        trans_list_frame = ttk.LabelFrame(trans_frame, text="قائمة الحركات", padding="5")
        trans_list_frame.pack(fill=tk.BOTH, expand=True)
        
        columns = ('التاريخ', 'النوع', 'الحساب', 'المبلغ', 'الوصف')
        self.transactions_tree = ttk.Treeview(trans_list_frame, columns=columns, show='headings', height=15)
        
        # تعريف الأعمدة
        for col in columns:
            self.transactions_tree.heading(col, text=col)
            
        self.transactions_tree.column('التاريخ', width=120, anchor=tk.CENTER)
        self.transactions_tree.column('النوع', width=100, anchor=tk.CENTER)
        self.transactions_tree.column('الحساب', width=150, anchor=tk.W)
        self.transactions_tree.column('المبلغ', width=100, anchor=tk.E)
        self.transactions_tree.column('الوصف', width=200, anchor=tk.W)
        
        # شريط التمرير
        scrollbar_trans = ttk.Scrollbar(trans_list_frame, orient=tk.VERTICAL, command=self.transactions_tree.yview)
        self.transactions_tree.configure(yscrollcommand=scrollbar_trans.set)
        
        # تخطيط الجدول
        self.transactions_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_trans.pack(side=tk.RIGHT, fill=tk.Y)
        
    def create_control_buttons(self, parent):
        """إنشاء أزرار التحكم"""
        buttons_frame = ttk.Frame(parent)
        buttons_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(buttons_frame, text="إغلاق", command=self.close_window).pack(side=tk.RIGHT)
        ttk.Button(buttons_frame, text="تقرير الخزنة", command=self.cash_report).pack(side=tk.RIGHT, padx=(0, 10))
        ttk.Button(buttons_frame, text="كشف حساب", command=self.account_statement).pack(side=tk.RIGHT, padx=(0, 10))
        
    def setup_keyboard_shortcuts(self):
        """إعداد اختصارات لوحة المفاتيح"""
        self.window.bind('<F1>', lambda e: self.execute_cash_operation())
        self.window.bind('<F2>', lambda e: self.save_account())
        self.window.bind('<F5>', lambda e: self.load_data())
        self.window.bind('<Escape>', lambda e: self.close_window())
        
    def load_data(self):
        """تحميل البيانات"""
        self.load_cash_balance()
        self.load_bank_accounts()
        self.load_transactions()
        
    def load_cash_balance(self):
        """تحميل رصيد الخزنة"""
        try:
            # حساب رصيد الخزنة من الحركات
            query = """
                SELECT 
                    SUM(CASE WHEN transaction_type = 'cash_in' THEN amount ELSE 0 END) -
                    SUM(CASE WHEN transaction_type = 'cash_out' THEN amount ELSE 0 END) as balance
                FROM cash_bank_transactions 
                WHERE transaction_type IN ('cash_in', 'cash_out')
            """
            result = self.db_manager.execute_query(query)
            balance = result[0][0] if result and result[0][0] else 0
            self.cash_balance_var.set(f"{balance:.2f} ج.م")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل رصيد الخزنة: {str(e)}")
            
    def load_bank_accounts(self):
        """تحميل الحسابات المصرفية"""
        try:
            # مسح البيانات الحالية
            for item in self.accounts_tree.get_children():
                self.accounts_tree.delete(item)
                
            # تحميل الحسابات
            query = "SELECT * FROM bank_accounts ORDER BY account_name"
            results = self.db_manager.execute_query(query)
            
            for row in results:
                self.accounts_tree.insert('', tk.END, values=(
                    row['account_name'],
                    row['bank_name'],
                    row['account_number'],
                    f"{row['balance']:.2f}"
                ))
                
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل الحسابات المصرفية: {str(e)}")
            
    def load_transactions(self):
        """تحميل الحركات"""
        try:
            # مسح البيانات الحالية
            for item in self.transactions_tree.get_children():
                self.transactions_tree.delete(item)
                
            # تحميل الحركات
            query = """
                SELECT cbt.*, ba.account_name 
                FROM cash_bank_transactions cbt
                LEFT JOIN bank_accounts ba ON cbt.account_id = ba.id
                ORDER BY cbt.transaction_date DESC
                LIMIT 100
            """
            results = self.db_manager.execute_query(query)
            
            for row in results:
                transaction_type = self.get_transaction_type_arabic(row['transaction_type'])
                account_name = row['account_name'] if row['account_name'] else 'الخزنة'
                
                self.transactions_tree.insert('', tk.END, values=(
                    row['transaction_date'][:16],  # التاريخ والوقت
                    transaction_type,
                    account_name,
                    f"{row['amount']:.2f}",
                    row['description'] or ''
                ))
                
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل الحركات: {str(e)}")
            
    def get_transaction_type_arabic(self, transaction_type):
        """تحويل نوع الحركة للعربية"""
        types = {
            'cash_in': 'إيداع خزنة',
            'cash_out': 'سحب خزنة',
            'bank_in': 'إيداع بنكي',
            'bank_out': 'سحب بنكي',
            'transfer': 'تحويل'
        }
        return types.get(transaction_type, transaction_type)
        
    def execute_cash_operation(self):
        """تنفيذ عملية خزنة"""
        try:
            operation = self.cash_operation_var.get()
            amount = float(self.cash_amount_var.get() or 0)
            description = self.cash_description_var.get().strip()
            
            if amount <= 0:
                messagebox.showwarning("تحذير", "يرجى إدخال مبلغ صحيح")
                return
                
            if not description:
                messagebox.showwarning("تحذير", "يرجى إدخال وصف للعملية")
                return
                
            # تحديد نوع الحركة
            transaction_type = 'cash_in' if operation == 'إيداع' else 'cash_out'
            
            # إدراج الحركة
            query = """
                INSERT INTO cash_bank_transactions (transaction_type, amount, description, transaction_date)
                VALUES (?, ?, ?, ?)
            """
            params = (transaction_type, amount, description, datetime.now().isoformat())
            self.db_manager.execute_insert(query, params)
            
            messagebox.showinfo("نجح", f"تم تنفيذ عملية {operation} بمبلغ {amount:.2f} ج.م بنجاح")
            
            # تحديث البيانات
            self.load_data()
            self.clear_cash_form()
            
        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال مبلغ صحيح")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تنفيذ العملية: {str(e)}")
            
    def clear_cash_form(self):
        """مسح نموذج الخزنة"""
        self.cash_amount_var.set('')
        self.cash_description_var.set('')
        
    def new_account(self):
        """حساب مصرفي جديد"""
        self.account_name_var.set('')
        self.bank_name_var.set('')
        self.account_number_var.set('')
        self.initial_balance_var.set('0')
        self.account_notes_text.delete(1.0, tk.END)
        
    def save_account(self):
        """حفظ الحساب المصرفي"""
        try:
            account_name = self.account_name_var.get().strip()
            bank_name = self.bank_name_var.get().strip()
            account_number = self.account_number_var.get().strip()
            initial_balance = float(self.initial_balance_var.get() or 0)
            notes = self.account_notes_text.get(1.0, tk.END).strip()
            
            if not account_name or not bank_name:
                messagebox.showwarning("تحذير", "يرجى إدخال اسم الحساب واسم البنك")
                return
                
            # إدراج الحساب الجديد
            query = """
                INSERT INTO bank_accounts (account_name, bank_name, account_number, balance, notes, created_date)
                VALUES (?, ?, ?, ?, ?, ?)
            """
            params = (account_name, bank_name, account_number, initial_balance, notes, datetime.now().isoformat())
            self.db_manager.execute_insert(query, params)
            
            messagebox.showinfo("نجح", "تم إضافة الحساب المصرفي بنجاح")
            
            # تحديث البيانات
            self.load_bank_accounts()
            self.new_account()
            
        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال رصيد صحيح")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في حفظ الحساب: {str(e)}")
            
    def delete_account(self):
        """حذف الحساب المصرفي"""
        selection = self.accounts_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار حساب للحذف")
            return
            
        item = self.accounts_tree.item(selection[0])
        account_name = item['values'][0]
        
        if messagebox.askyesno("تأكيد الحذف", f"هل تريد حذف الحساب '{account_name}'؟"):
            try:
                query = "DELETE FROM bank_accounts WHERE account_name = ?"
                self.db_manager.execute_query(query, (account_name,))
                messagebox.showinfo("نجح", "تم حذف الحساب بنجاح")
                self.load_bank_accounts()
                self.new_account()
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في حذف الحساب: {str(e)}")
                
    def search_transactions(self):
        """البحث في الحركات"""
        messagebox.showinfo("قريباً", "سيتم تطوير البحث في الحركات قريباً")
        
    def cash_report(self):
        """تقرير الخزنة"""
        messagebox.showinfo("قريباً", "سيتم تطوير تقرير الخزنة قريباً")
        
    def account_statement(self):
        """كشف حساب"""
        messagebox.showinfo("قريباً", "سيتم تطوير كشف الحساب قريباً")
        
    def close_window(self):
        """إغلاق النافذة"""
        self.window.destroy()
