#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف التكوين للنظام
System Configuration File
"""

import os
from datetime import datetime

# معلومات التطبيق
APP_NAME = "نظام المحاسبة المتكامل"
APP_NAME_EN = "Integrated Accounting System"
APP_VERSION = "1.0.0"
APP_AUTHOR = "مساعد الذكي"
APP_DESCRIPTION = "نظام محاسبة شامل ومتكامل"

# إعدادات قاعدة البيانات
DATABASE_NAME = "accounting_system.db"
DATABASE_PATH = os.path.join(os.path.dirname(__file__), DATABASE_NAME)

# إعدادات النسخ الاحتياطي
BACKUP_ENABLED = True
BACKUP_FOLDER = "backups"
BACKUP_INTERVAL_DAYS = 7  # عمل نسخة احتياطية كل 7 أيام

# إعدادات التقارير
REPORTS_FOLDER = "reports"
DEFAULT_REPORT_FORMAT = "PDF"
SUPPORTED_FORMATS = ["PDF", "Excel", "CSV"]

# إعدادات الواجهة
DEFAULT_FONT = "Arial"
DEFAULT_FONT_SIZE = 12
WINDOW_THEME = "default"

# إعدادات الطباعة
DEFAULT_PRINTER = None
PAPER_SIZE = "A4"
PRINT_MARGINS = {
    "top": 20,
    "bottom": 20,
    "left": 20,
    "right": 20
}

# إعدادات العملة
CURRENCY_SYMBOL = "ج.م"
CURRENCY_NAME = "جنيه مصري"
DECIMAL_PLACES = 2

# إعدادات الضرائب
DEFAULT_TAX_RATE = 0.14  # 14%
TAX_ENABLED = True

# إعدادات الباركود
BARCODE_FORMAT = "CODE128"
BARCODE_WIDTH = 2
BARCODE_HEIGHT = 50

# إعدادات الأمان
SESSION_TIMEOUT = 3600  # ساعة واحدة بالثواني
AUTO_LOGOUT_ENABLED = False
PASSWORD_MIN_LENGTH = 6

# إعدادات التصدير
EXPORT_FOLDER = "exports"
DEFAULT_EXPORT_FORMAT = "Excel"

# إعدادات اللغة
DEFAULT_LANGUAGE = "ar"  # العربية
SUPPORTED_LANGUAGES = ["ar", "en"]

# إعدادات السجلات
LOGGING_ENABLED = True
LOG_FOLDER = "logs"
LOG_LEVEL = "INFO"
LOG_MAX_SIZE = 10 * 1024 * 1024  # 10 ميجابايت

# إعدادات الإشعارات
NOTIFICATIONS_ENABLED = True
LOW_STOCK_ALERT = True
PAYMENT_DUE_ALERT = True
BACKUP_REMINDER = True

# مسارات المجلدات
def ensure_folders():
    """إنشاء المجلدات المطلوبة إذا لم تكن موجودة"""
    folders = [
        BACKUP_FOLDER,
        REPORTS_FOLDER,
        EXPORT_FOLDER,
        LOG_FOLDER,
        "assets"
    ]
    
    for folder in folders:
        if not os.path.exists(folder):
            os.makedirs(folder)

# دالة الحصول على معلومات النظام
def get_system_info():
    """الحصول على معلومات النظام"""
    return {
        "app_name": APP_NAME,
        "app_name_en": APP_NAME_EN,
        "version": APP_VERSION,
        "author": APP_AUTHOR,
        "description": APP_DESCRIPTION,
        "database_path": DATABASE_PATH,
        "current_time": datetime.now().isoformat()
    }

# دالة تحديث التكوين
def update_config(key, value):
    """تحديث قيمة في التكوين"""
    globals()[key] = value

# دالة الحصول على قيمة من التكوين
def get_config(key, default=None):
    """الحصول على قيمة من التكوين"""
    return globals().get(key, default)

# تهيئة المجلدات عند استيراد الملف
ensure_folders()
