#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة إدارة المنتجات
Products Management Window
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# إضافة مسار المجلدات للاستيراد
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'database'))

from database.models import Product

class ProductsWindow:
    """فئة نافذة إدارة المنتجات"""
    
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.window = tk.Toplevel(parent)
        self.products = []
        self.selected_product = None
        
        self.setup_window()
        self.setup_ui()
        self.load_products()
        self.setup_keyboard_shortcuts()
        
    def setup_window(self):
        """إعداد النافذة"""
        self.window.title("إدارة المنتجات")
        self.window.geometry("1000x700")
        self.window.resizable(True, True)
        
        # جعل النافذة في المقدمة
        self.window.transient(self.parent)
        self.window.grab_set()
        
        # توسيط النافذة
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (1000 // 2)
        y = (self.window.winfo_screenheight() // 2) - (700 // 2)
        self.window.geometry(f"1000x700+{x}+{y}")
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.window, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # شريط البحث والأدوات
        self.create_toolbar(main_frame)
        
        # منطقة المحتوى الرئيسي
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))
        
        # قائمة المنتجات (الجانب الأيسر)
        self.create_products_list(content_frame)
        
        # تفاصيل المنتج (الجانب الأيمن)
        self.create_product_details(content_frame)
        
        # أزرار التحكم
        self.create_control_buttons(main_frame)
        
    def create_toolbar(self, parent):
        """إنشاء شريط الأدوات"""
        toolbar = ttk.Frame(parent)
        toolbar.pack(fill=tk.X, pady=(0, 10))
        
        # البحث
        ttk.Label(toolbar, text="البحث:").pack(side=tk.LEFT)
        self.search_var = tk.StringVar()
        search_entry = ttk.Entry(toolbar, textvariable=self.search_var, width=30)
        search_entry.pack(side=tk.LEFT, padx=(5, 10))
        search_entry.bind('<KeyRelease>', self.search_products)
        self.search_entry = search_entry
        
        ttk.Button(toolbar, text="بحث", command=self.search_products).pack(side=tk.LEFT, padx=(0, 20))
        
        # أزرار سريعة
        ttk.Button(toolbar, text="منتج جديد", command=self.new_product).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="تحديث", command=self.load_products).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="مخزون منخفض", command=self.show_low_stock).pack(side=tk.LEFT, padx=(0, 5))
        
    def create_products_list(self, parent):
        """إنشاء قائمة المنتجات"""
        # إطار قائمة المنتجات
        list_frame = ttk.LabelFrame(parent, text="قائمة المنتجات", padding="5")
        list_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        # جدول المنتجات
        columns = ('الاسم', 'الباركود', 'الوحدة', 'الكمية', 'سعر الشراء', 'سعر البيع')
        self.products_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)
        
        # تعريف الأعمدة
        self.products_tree.heading('الاسم', text='الاسم')
        self.products_tree.heading('الباركود', text='الباركود')
        self.products_tree.heading('الوحدة', text='الوحدة')
        self.products_tree.heading('الكمية', text='الكمية')
        self.products_tree.heading('سعر الشراء', text='سعر الشراء')
        self.products_tree.heading('سعر البيع', text='سعر البيع')
        
        self.products_tree.column('الاسم', width=150, anchor=tk.W)
        self.products_tree.column('الباركود', width=120, anchor=tk.CENTER)
        self.products_tree.column('الوحدة', width=80, anchor=tk.CENTER)
        self.products_tree.column('الكمية', width=80, anchor=tk.E)
        self.products_tree.column('سعر الشراء', width=100, anchor=tk.E)
        self.products_tree.column('سعر البيع', width=100, anchor=tk.E)
        
        # شريط التمرير
        scrollbar_list = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.products_tree.yview)
        self.products_tree.configure(yscrollcommand=scrollbar_list.set)
        
        # تخطيط الجدول وشريط التمرير
        self.products_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_list.pack(side=tk.RIGHT, fill=tk.Y)
        
        # ربط الأحداث
        self.products_tree.bind('<<TreeviewSelect>>', self.on_product_select)
        self.products_tree.bind('<Double-1>', self.edit_product)
        
    def create_product_details(self, parent):
        """إنشاء منطقة تفاصيل المنتج"""
        # إطار التفاصيل
        details_frame = ttk.LabelFrame(parent, text="تفاصيل المنتج", padding="10")
        details_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # الاسم
        ttk.Label(details_frame, text="اسم المنتج:").grid(row=0, column=0, sticky=tk.W, pady=(0, 10))
        self.name_var = tk.StringVar()
        ttk.Entry(details_frame, textvariable=self.name_var, width=30).grid(row=0, column=1, sticky=tk.W+tk.E, pady=(0, 10))
        
        # الباركود
        ttk.Label(details_frame, text="الباركود:").grid(row=1, column=0, sticky=tk.W, pady=(0, 10))
        self.barcode_var = tk.StringVar()
        barcode_frame = ttk.Frame(details_frame)
        barcode_frame.grid(row=1, column=1, sticky=tk.W+tk.E, pady=(0, 10))
        ttk.Entry(barcode_frame, textvariable=self.barcode_var, width=25).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(barcode_frame, text="مسح", command=self.scan_barcode).pack(side=tk.RIGHT, padx=(5, 0))
        
        # الوحدة
        ttk.Label(details_frame, text="الوحدة:").grid(row=2, column=0, sticky=tk.W, pady=(0, 10))
        self.unit_var = tk.StringVar(value="قطعة")
        unit_combo = ttk.Combobox(details_frame, textvariable=self.unit_var, width=27)
        unit_combo['values'] = ('قطعة', 'كيلو', 'جرام', 'لتر', 'متر', 'علبة', 'كرتونة', 'دستة')
        unit_combo.grid(row=2, column=1, sticky=tk.W+tk.E, pady=(0, 10))
        
        # الفئة
        ttk.Label(details_frame, text="الفئة:").grid(row=3, column=0, sticky=tk.W, pady=(0, 10))
        self.category_var = tk.StringVar()
        ttk.Entry(details_frame, textvariable=self.category_var, width=30).grid(row=3, column=1, sticky=tk.W+tk.E, pady=(0, 10))
        
        # سعر الشراء
        ttk.Label(details_frame, text="سعر الشراء:").grid(row=4, column=0, sticky=tk.W, pady=(0, 10))
        self.purchase_price_var = tk.StringVar()
        ttk.Entry(details_frame, textvariable=self.purchase_price_var, width=30).grid(row=4, column=1, sticky=tk.W+tk.E, pady=(0, 10))
        
        # سعر البيع
        ttk.Label(details_frame, text="سعر البيع:").grid(row=5, column=0, sticky=tk.W, pady=(0, 10))
        self.sale_price_var = tk.StringVar()
        ttk.Entry(details_frame, textvariable=self.sale_price_var, width=30).grid(row=5, column=1, sticky=tk.W+tk.E, pady=(0, 10))
        
        # الكمية الحالية
        ttk.Label(details_frame, text="الكمية الحالية:").grid(row=6, column=0, sticky=tk.W, pady=(0, 10))
        self.quantity_var = tk.StringVar()
        ttk.Entry(details_frame, textvariable=self.quantity_var, width=30).grid(row=6, column=1, sticky=tk.W+tk.E, pady=(0, 10))
        
        # الحد الأدنى للكمية
        ttk.Label(details_frame, text="الحد الأدنى:").grid(row=7, column=0, sticky=tk.W, pady=(0, 10))
        self.min_quantity_var = tk.StringVar()
        ttk.Entry(details_frame, textvariable=self.min_quantity_var, width=30).grid(row=7, column=1, sticky=tk.W+tk.E, pady=(0, 10))
        
        # الملاحظات
        ttk.Label(details_frame, text="الملاحظات:").grid(row=8, column=0, sticky=tk.W+tk.N, pady=(0, 10))
        notes_text = tk.Text(details_frame, width=30, height=4)
        notes_text.grid(row=8, column=1, sticky=tk.W+tk.E, pady=(0, 10))
        self.notes_text = notes_text
        
        # تكوين الأعمدة للتمدد
        details_frame.columnconfigure(1, weight=1)
        
        # أزرار العمليات
        buttons_frame = ttk.Frame(details_frame)
        buttons_frame.grid(row=9, column=0, columnspan=2, pady=(20, 0), sticky=tk.W+tk.E)
        
        ttk.Button(buttons_frame, text="حفظ", command=self.save_product).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(buttons_frame, text="حذف", command=self.delete_product).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(buttons_frame, text="مسح", command=self.clear_form).pack(side=tk.LEFT, padx=(0, 5))
        
    def create_control_buttons(self, parent):
        """إنشاء أزرار التحكم"""
        buttons_frame = ttk.Frame(parent)
        buttons_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(buttons_frame, text="إغلاق", command=self.close_window).pack(side=tk.RIGHT)
        ttk.Button(buttons_frame, text="تقرير المخزون", command=self.inventory_report).pack(side=tk.RIGHT, padx=(0, 10))
        ttk.Button(buttons_frame, text="طباعة الباركود", command=self.print_barcode).pack(side=tk.RIGHT, padx=(0, 10))
        
    def setup_keyboard_shortcuts(self):
        """إعداد اختصارات لوحة المفاتيح"""
        self.window.bind('<F1>', lambda e: self.new_product())
        self.window.bind('<F2>', lambda e: self.save_product())
        self.window.bind('<F3>', lambda e: self.delete_product())
        self.window.bind('<F5>', lambda e: self.load_products())
        self.window.bind('<F6>', lambda e: self.scan_barcode())
        self.window.bind('<Escape>', lambda e: self.close_window())
        self.window.bind('<Control-f>', lambda e: self.search_entry.focus())
        
    def load_products(self):
        """تحميل المنتجات من قاعدة البيانات"""
        try:
            # مسح البيانات الحالية
            for item in self.products_tree.get_children():
                self.products_tree.delete(item)
                
            # تحميل المنتجات
            self.products = Product.get_all(self.db_manager)
            
            # إضافة المنتجات للجدول
            for product in self.products:
                # تلوين المنتجات منخفضة المخزون
                tags = ()
                if product.quantity <= product.min_quantity:
                    tags = ('low_stock',)
                    
                self.products_tree.insert('', tk.END, values=(
                    product.name,
                    product.barcode,
                    product.unit,
                    f"{product.quantity:.2f}",
                    f"{product.purchase_price:.2f}",
                    f"{product.sale_price:.2f}"
                ), tags=tags)
                
            # تكوين ألوان المخزون المنخفض
            self.products_tree.tag_configure('low_stock', background='#ffcccc')
                
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل المنتجات: {str(e)}")
            
    def search_products(self, event=None):
        """البحث عن المنتجات"""
        search_term = self.search_var.get().strip()
        
        try:
            # مسح البيانات الحالية
            for item in self.products_tree.get_children():
                self.products_tree.delete(item)
                
            if search_term:
                # البحث (سيتم تطويره في models.py)
                products = [p for p in Product.get_all(self.db_manager) 
                           if search_term.lower() in p.name.lower() or 
                              search_term.lower() in p.barcode.lower()]
            else:
                # عرض جميع المنتجات
                products = Product.get_all(self.db_manager)
                
            self.products = products
            
            # إضافة النتائج للجدول
            for product in products:
                tags = ()
                if product.quantity <= product.min_quantity:
                    tags = ('low_stock',)
                    
                self.products_tree.insert('', tk.END, values=(
                    product.name,
                    product.barcode,
                    product.unit,
                    f"{product.quantity:.2f}",
                    f"{product.purchase_price:.2f}",
                    f"{product.sale_price:.2f}"
                ), tags=tags)
                
            self.products_tree.tag_configure('low_stock', background='#ffcccc')
                
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في البحث: {str(e)}")
            
    def show_low_stock(self):
        """عرض المنتجات منخفضة المخزون"""
        try:
            # مسح البيانات الحالية
            for item in self.products_tree.get_children():
                self.products_tree.delete(item)
                
            # تحميل المنتجات منخفضة المخزون
            products = Product.get_low_stock(self.db_manager)
            self.products = products
            
            # إضافة النتائج للجدول
            for product in products:
                self.products_tree.insert('', tk.END, values=(
                    product.name,
                    product.barcode,
                    product.unit,
                    f"{product.quantity:.2f}",
                    f"{product.purchase_price:.2f}",
                    f"{product.sale_price:.2f}"
                ), tags=('low_stock',))
                
            self.products_tree.tag_configure('low_stock', background='#ffcccc')
            
            if not products:
                messagebox.showinfo("معلومات", "لا توجد منتجات منخفضة المخزون")
                
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل المنتجات منخفضة المخزون: {str(e)}")
            
    def on_product_select(self, event):
        """عند اختيار منتج من القائمة"""
        selection = self.products_tree.selection()
        if selection:
            item = self.products_tree.item(selection[0])
            product_name = item['values'][0]
            
            # البحث عن المنتج في القائمة
            for product in self.products:
                if product.name == product_name:
                    self.selected_product = product
                    self.load_product_details(product)
                    break
                    
    def load_product_details(self, product):
        """تحميل تفاصيل المنتج في النموذج"""
        self.name_var.set(product.name)
        self.barcode_var.set(product.barcode)
        self.unit_var.set(product.unit)
        self.category_var.set(product.category)
        self.purchase_price_var.set(str(product.purchase_price))
        self.sale_price_var.set(str(product.sale_price))
        self.quantity_var.set(str(product.quantity))
        self.min_quantity_var.set(str(product.min_quantity))
        
        # الملاحظات
        self.notes_text.delete(1.0, tk.END)
        self.notes_text.insert(1.0, product.notes)
        
    def clear_form(self):
        """مسح النموذج"""
        self.selected_product = None
        self.name_var.set('')
        self.barcode_var.set('')
        self.unit_var.set('قطعة')
        self.category_var.set('')
        self.purchase_price_var.set('0')
        self.sale_price_var.set('0')
        self.quantity_var.set('0')
        self.min_quantity_var.set('0')
        self.notes_text.delete(1.0, tk.END)
        
    def new_product(self):
        """منتج جديد"""
        self.clear_form()
        
    def save_product(self):
        """حفظ المنتج"""
        try:
            name = self.name_var.get().strip()
            if not name:
                messagebox.showwarning("تحذير", "يرجى إدخال اسم المنتج")
                return
                
            barcode = self.barcode_var.get().strip()
            unit = self.unit_var.get().strip()
            category = self.category_var.get().strip()
            notes = self.notes_text.get(1.0, tk.END).strip()
            
            try:
                purchase_price = float(self.purchase_price_var.get() or 0)
                sale_price = float(self.sale_price_var.get() or 0)
                quantity = float(self.quantity_var.get() or 0)
                min_quantity = float(self.min_quantity_var.get() or 0)
            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال أرقام صحيحة للأسعار والكميات")
                return
                
            if self.selected_product:
                # تحديث منتج موجود
                self.selected_product.name = name
                self.selected_product.barcode = barcode
                self.selected_product.unit = unit
                self.selected_product.category = category
                self.selected_product.purchase_price = purchase_price
                self.selected_product.sale_price = sale_price
                self.selected_product.quantity = quantity
                self.selected_product.min_quantity = min_quantity
                self.selected_product.notes = notes
                self.selected_product.save()
                messagebox.showinfo("نجح", "تم تحديث المنتج بنجاح")
            else:
                # إضافة منتج جديد
                product = Product(self.db_manager, name=name, barcode=barcode, unit=unit,
                                purchase_price=purchase_price, sale_price=sale_price,
                                quantity=quantity, min_quantity=min_quantity,
                                category=category, notes=notes)
                product.save()
                messagebox.showinfo("نجح", "تم إضافة المنتج بنجاح")
                
            # تحديث القائمة
            self.load_products()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في حفظ المنتج: {str(e)}")
            
    def delete_product(self):
        """حذف المنتج"""
        if not self.selected_product:
            messagebox.showwarning("تحذير", "يرجى اختيار منتج للحذف")
            return
            
        if messagebox.askyesno("تأكيد الحذف", 
                              f"هل تريد حذف المنتج '{self.selected_product.name}'؟\nهذا الإجراء لا يمكن التراجع عنه"):
            try:
                self.selected_product.delete()
                messagebox.showinfo("نجح", "تم حذف المنتج بنجاح")
                self.clear_form()
                self.load_products()
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في حذف المنتج: {str(e)}")
                
    def edit_product(self, event):
        """تعديل المنتج (عند النقر المزدوج)"""
        self.on_product_select(event)
        
    def scan_barcode(self):
        """مسح الباركود"""
        messagebox.showinfo("قريباً", "سيتم تطوير قارئ الباركود قريباً")
        
    def print_barcode(self):
        """طباعة الباركود"""
        if not self.selected_product:
            messagebox.showwarning("تحذير", "يرجى اختيار منتج لطباعة الباركود")
            return
        messagebox.showinfo("قريباً", "سيتم تطوير طباعة الباركود قريباً")
        
    def inventory_report(self):
        """تقرير المخزون"""
        messagebox.showinfo("قريباً", "سيتم تطوير تقرير المخزون قريباً")
        
    def close_window(self):
        """إغلاق النافذة"""
        self.window.destroy()
